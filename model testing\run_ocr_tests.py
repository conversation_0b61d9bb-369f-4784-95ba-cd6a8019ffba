#!/usr/bin/env python3
"""
Main script to run OCR model comparison tests
"""
import argparse
import logging
import sys
from pathlib import Path
import json

from ocr_tester import OCRTester
from metrics_analyzer import OCRMetricsAnalyzer
from config import INPUT_DIR, OUTPUT_DIR

def setup_logging(verbose: bool = False):
    """Setup logging configuration"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(OUTPUT_DIR / 'ocr_testing.log')
        ]
    )

def main():
    parser = argparse.ArgumentParser(description='OCR Model Comparison Testing')
    parser.add_argument('--input-dir', type=str, default=str(INPUT_DIR),
                       help='Directory containing test images')
    parser.add_argument('--output-dir', type=str, default=str(OUTPUT_DIR),
                       help='Directory to save results')
    parser.add_argument('--ground-truth', type=str,
                       help='JSON file with ground truth texts')
    parser.add_argument('--single-image', type=str,
                       help='Test a single image file')
    parser.add_argument('--models', nargs='+', 
                       choices=['tesseract', 'easyocr', 'paddleocr', 'trocr'],
                       help='Specific models to test (default: all enabled)')
    parser.add_argument('--no-visualizations', action='store_true',
                       help='Skip generating visualization plots')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    # Create output directory
    output_path = Path(args.output_dir)
    output_path.mkdir(exist_ok=True)
    
    logger.info("Starting OCR model comparison tests")
    logger.info(f"Input directory: {args.input_dir}")
    logger.info(f"Output directory: {args.output_dir}")
    
    try:
        # Initialize OCR tester
        config_override = {}
        if args.models:
            # Disable all models first, then enable selected ones
            from config import OCR_MODELS
            for model_name in OCR_MODELS:
                config_override[model_name] = OCR_MODELS[model_name].copy()
                config_override[model_name]['enabled'] = model_name in args.models
        
        tester = OCRTester(config_override)
        
        if not tester.models:
            logger.error("No OCR models available. Check your configuration and dependencies.")
            return 1
        
        logger.info(f"Testing with models: {list(tester.models.keys())}")
        
        # Run tests
        if args.single_image:
            logger.info(f"Testing single image: {args.single_image}")
            if not Path(args.single_image).exists():
                logger.error(f"Image file not found: {args.single_image}")
                return 1
            
            results = tester.test_single_image(args.single_image)
        else:
            logger.info(f"Testing all images in directory: {args.input_dir}")
            if not Path(args.input_dir).exists():
                logger.error(f"Input directory not found: {args.input_dir}")
                return 1
            
            results = tester.test_directory(args.input_dir, args.ground_truth)
        
        if not results:
            logger.warning("No results generated")
            return 1
        
        # Save results
        logger.info("Saving results...")
        tester.save_results(args.output_dir)
        
        # Print summary
        tester.print_summary()
        
        # Generate analysis and visualizations
        logger.info("Generating analysis...")
        analyzer = OCRMetricsAnalyzer(results_data=results)
        
        # Save detailed analysis report
        analysis_report = analyzer.generate_performance_report()
        report_file = output_path / "analysis_report.json"
        analyzer.save_report(str(report_file))
        
        # Generate visualizations
        if not args.no_visualizations:
            logger.info("Creating visualizations...")
            viz_dir = output_path / "visualizations"
            analyzer.create_visualizations(str(viz_dir))
        
        # Print key findings
        print("\n" + "="*60)
        print("KEY FINDINGS")
        print("="*60)
        
        for recommendation in analysis_report['recommendations']:
            print(f"• {recommendation}")
        
        print(f"\nDetailed results saved to: {args.output_dir}")
        print(f"Analysis report: {report_file}")
        
        if not args.no_visualizations:
            print(f"Visualizations: {viz_dir}")
        
        logger.info("OCR testing completed successfully")
        return 0
        
    except Exception as e:
        logger.error(f"Error during testing: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
