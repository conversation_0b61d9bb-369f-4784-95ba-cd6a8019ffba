# Enhanced Invoice Parser with Handwritten Support

## 🚀 New Command-Line Features

Your existing invoice extractor now includes powerful new features for handwritten documents and enhanced image processing, all integrated as command-line options.

## 📋 Command-Line Options

### **Processing Modes**

| Option | Description | Use Case |
|--------|-------------|----------|
| `--handwritten` | Enhanced handwritten processing | Handwritten receipts/invoices |
| `--img` | Force Qwen2-VL vision model | Maximum accuracy for images |
| `--disable-vision` | OCR only mode | Faster processing |
| (default) | Standard mode | Regular documents |

### **Core Options**
- `-i, --input`: Input file or directory
- `-o, --output`: Output CSV file path
- `--batch`: Process all files in directory
- `--save-json`: Export to JSON format
- `--save-raw-text`: Save extracted text
- `--verbose`: Detailed logging

## 🖋️ Handwritten Document Processing

### **Features**
- **Advanced OCR**: Multiple Tesseract configurations optimized for handwriting
- **Image Preprocessing**: Adaptive thresholding, noise reduction, resolution enhancement
- **LLM Correction**: Groq LLM fixes OCR errors and unclear text
- **Enhanced Extraction**: Specialized prompting for handwritten content

### **Usage**
```bash
# Single handwritten document
python 2test.py -i handwritten_receipt.jpg -o results.csv --handwritten

# Batch process handwritten documents
python 2test.py -i ./handwritten_docs/ -o batch_results.csv --batch --handwritten

# Handwritten with JSON output
python 2test.py -i receipt.png -o results.csv --handwritten --save-json
```

## 🖼️ Enhanced Image Processing

### **Features**
- **Qwen2-VL Vision Model**: State-of-the-art image-to-text extraction
- **High Accuracy**: Superior recognition for complex layouts
- **Multi-format Support**: PNG, JPG, JPEG, TIFF, BMP, WEBP, GIF

### **Usage**
```bash
# Force image processing mode
python 2test.py -i complex_invoice.png -o results.csv --img

# Image mode with verbose output
python 2test.py -i document.jpg -o results.csv --img --verbose
```

## 📊 Output Formats

### **Enhanced CSV Structure**
The parser now exports 25+ fields including:
- **Basic Info**: invoice_number, invoice_date, due_date, purchase_order_number
- **Vendor Details**: name, address, phone, email
- **Customer Details**: name, address, phone, email
- **Financial**: subtotal, tax_type, tax_rate, tax_amount, discount_amount, shipping_amount, total
- **Payment**: payment_terms, payment_method
- **Items**: name, category, quantity, unit_price, total_price
- **Additional**: notes, **other_information** (captures handwritten notes)

### **JSON Export**
Complete structured data with processing metadata:
```json
{
  "success": true,
  "extraction_method": "handwritten_llm",
  "invoice_data": {
    "total": 25.50,
    "vendor": {"name": "Joe's Store"},
    "items": [{"name": "Coffee", "total_price": 6.50}],
    "other_information": "Handwritten note: Thanks!"
  }
}
```

## 🎯 Usage Examples

### **1. Standard Processing**
```bash
python 2test.py -i invoice.pdf -o results.csv
```
Uses Qwen2-VL vision model with OCR fallback.

### **2. Handwritten Documents**
```bash
python 2test.py -i handwritten_receipt.jpg -o results.csv --handwritten
```
Enhanced OCR + LLM correction for handwritten text.

### **3. Maximum Image Accuracy**
```bash
python 2test.py -i complex_document.png -o results.csv --img
```
Forces Qwen2-VL vision model for best image recognition.

### **4. Fast OCR Processing**
```bash
python 2test.py -i simple_invoice.pdf -o results.csv --disable-vision
```
Tesseract OCR only for faster processing.

### **5. Batch Processing**
```bash
python 2test.py -i ./invoices/ -o batch_results.csv --batch --handwritten
```
Process entire directory with handwritten mode.

### **6. Complete Export**
```bash
python 2test.py -i document.jpg -o results.csv --handwritten --save-json --save-raw-text
```
Export CSV, JSON, and raw text files.

## 🔧 Processing Pipeline

### **Standard Mode**
1. Qwen2-VL vision model extraction
2. OCR fallback if needed
3. LLM parsing with standard prompts
4. Structured data export

### **Handwritten Mode (`--handwritten`)**
1. Image preprocessing (enhancement, noise reduction)
2. Multiple OCR configurations (PSM 6, 7, 8)
3. LLM text correction for OCR errors
4. Specialized handwritten prompting
5. Enhanced field extraction

### **Image Mode (`--img`)**
1. Force Qwen2-VL vision model
2. High-quality image-to-text extraction
3. Advanced layout understanding
4. Comprehensive data extraction

## 📈 Performance Comparison

| Mode | Speed | Accuracy | Best For |
|------|-------|----------|----------|
| Standard | Fast | High | Regular documents |
| Handwritten | Slower | Highest* | Handwritten text |
| Image | Medium | Highest | Complex layouts |
| OCR Only | Fastest | Medium | Simple documents |

*Highest for handwritten content

## 🛠️ Installation & Setup

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set API key:**
   ```bash
   export GROQ_API_KEY="your_key_here"
   # or create .env file
   ```

3. **Test installation:**
   ```bash
   python 2test.py --help
   ```

## 🧪 Demo & Testing

Run the demo to see all features:
```bash
python demo_enhanced_features.py
```

This will test all processing modes with your files and show the differences.

## 💡 Best Practices

### **For Handwritten Documents**
- Use `--handwritten` flag
- Ensure 300+ DPI resolution
- Good lighting and contrast
- Flat, straight documents

### **For Complex Images**
- Use `--img` flag for maximum accuracy
- High-resolution images work best
- Clear, unobstructed text

### **For Batch Processing**
- Use `--batch` with directory input
- Combine with `--handwritten` for handwritten batches
- Add `--save-json` for structured data

### **For Performance**
- Use `--disable-vision` for speed
- Standard mode for balanced performance
- Handwritten mode only when needed

## 🚨 Error Handling

The parser includes robust error handling:
- Graceful fallbacks between processing modes
- Detailed error messages in output
- Partial extraction when possible
- Processing method tracking

## 📋 Output Files

Generated files include:
- **CSV**: Enhanced structure with 25+ fields
- **JSON**: Complete structured data (with `--save-json`)
- **TXT**: Raw extracted text (with `--save-raw-text`)

All files maintain consistent naming and include processing metadata.
