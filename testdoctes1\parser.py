import os
import time
import logging
import csv
from pathlib import Path
from typing import Optional, Union, List, Dict, Any
import json
import re


import pytesseract
from PIL import Image
import fitz  


from groq import Groq
import instructor


from models import InvoiceData, DocumentParsingResult, LineItem, ContactInfo, TaxInfo


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DocumentParser:
    
    def __init__(self, groq_api_key: Optional[str] = None):
        self.groq_api_key = groq_api_key or os.getenv("GROQ_API_KEY")
        if not self.groq_api_key:
            raise ValueError("GROQ_API_KEY environment variable or parameter required")
        
        
        self.client = Groq(api_key=self.groq_api_key)
        self.client = instructor.from_groq(self.client)
        
        
        self.supported_extensions = {'.pdf', '.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.txt'}
    
    def detect_document_type(self, file_path: Path) -> str:
        extension = file_path.suffix.lower()
        
        if extension == '.pdf':
            return 'pdf'
        elif extension in {'.png', '.jpg', '.jpeg', '.tiff', '.bmp'}:
            return 'image'
        elif extension == '.txt':
            return 'text'
        else:
            raise ValueError(f"Unsupported file type: {extension}")
    
    def extract_text_from_pdf(self, file_path: Path) -> str:
        try:
            doc = fitz.open(file_path)
            text = ""
            
            for page_num in range(doc.page_count):
                page = doc[page_num]
                text += page.get_text()
            
            doc.close()
            return text.strip()
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {e}")
            
            return self.extract_text_with_ocr(file_path)
    
    def extract_text_with_ocr(self, file_path: Path) -> str:
        try:
            if file_path.suffix.lower() == '.pdf':
                
                doc = fitz.open(file_path)
                text = ""
                
                for page_num in range(doc.page_count):
                    page = doc[page_num]
                    pix = page.get_pixmap()
                    img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
                    
                    
                    custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,!?@'
                    page_text = pytesseract.image_to_string(img, config=custom_config)
                    text += page_text + "\n"
                
                doc.close()
                return text.strip()
            else:
                
                img = Image.open(file_path)
                custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,!?@'
                text = pytesseract.image_to_string(img, config=custom_config)
                return text.strip()
        
        except Exception as e:
            logger.error(f"Error during OCR: {e}")
            return ""
    
    def extract_text(self, file_path: Path) -> str:
        """Extract text from document based on type."""
        doc_type = self.detect_document_type(file_path)
        
        if doc_type == 'text':
            return file_path.read_text(encoding='utf-8')
        elif doc_type == 'pdf':
            return self.extract_text_from_pdf(file_path)
        elif doc_type == 'image':
            return self.extract_text_with_ocr(file_path)
        else:
            raise ValueError(f"Unsupported document type: {doc_type}")
    
    def create_extraction_prompt(self, text: str) -> str:

        return f"""
You are an expert document parser. Extract structured invoice/receipt data from the following text.

IMPORTANT INSTRUCTIONS:
1. Extract ALL line items with their names, quantities, and prices
2. Identify vendor and customer information
3. Find dates, invoice numbers, and totals
4. Handle handwritten or poorly formatted text
5. If information is unclear, make reasonable assumptions
6. Ensure all prices are positive numbers
7. Clean up OCR artifacts and formatting issues

TEXT TO PARSE:
{text}

Extract the data according to the InvoiceData schema. Be thorough and accurate.
"""
    
    def parse_with_llm(self, text: str) -> Optional[InvoiceData]:
        try:
            prompt = self.create_extraction_prompt(text)
            
            response = self.client.chat.completions.create(
                model="llama-3.3-70b-versatile",
                messages=[
                    {"role": "system", "content": "You are an expert invoice parser. Extract structured data accurately from documents."},
                    {"role": "user", "content": prompt}
                ],
                response_model=InvoiceData,
                max_retries=3
            )
            
            return response
        
        except Exception as e:
            logger.error(f"LLM parsing error: {e}")
            return None
    
    def fallback_regex_extraction(self, text: str) -> InvoiceData:
        """Fallback regex-based extraction when LLM fails."""
        logger.info("Using fallback regex extraction")
        
        
        total_patterns = [
            r'total[:\s]*\$?([0-9,]+\.?[0-9]*)',
            r'amount[:\s]*\$?([0-9,]+\.?[0-9]*)',
            r'grand\s+total[:\s]*\$?([0-9,]+\.?[0-9]*)'
        ]
        
        date_patterns = [
            r'(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',
            r'(\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2})',
            r'((?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*\s+\d{1,2},?\s+\d{4})'
        ]
        
        
        total = 0.0
        for pattern in total_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                total_str = match.group(1).replace(',', '')
                try:
                    total = float(total_str)
                    break
                except ValueError:
                    continue
        
        
        invoice_date = None
        for pattern in date_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                invoice_date = match.group(1)
                break
        
        
        items = []
        lines = text.split('\n')
        for line in lines:
            
            price_match = re.search(r'(.+?)\s+\$?([0-9,]+\.?[0-9]*)', line)
            if price_match:
                name = price_match.group(1).strip()
                price_str = price_match.group(2).replace(',', '')
                try:
                    price = float(price_str)
                    if len(name) > 3 and price > 0:
                        items.append(LineItem(name=name, total_price=price))
                except ValueError:
                    continue
        
        return InvoiceData(
            total=total,
            invoice_date=invoice_date,
            items=items
        )
    
    def parse_document(self, file_path: Union[str, Path]) -> DocumentParsingResult:
        start_time = time.time()
        file_path = Path(file_path)
        
        if not file_path.exists():
            return DocumentParsingResult(
                success=False,
                error_message=f"File not found: {file_path}"
            )
        
        if file_path.suffix.lower() not in self.supported_extensions:
            return DocumentParsingResult(
                success=False,
                error_message=f"Unsupported file type: {file_path.suffix}"
            )
        
        try:
            
            logger.info(f"Extracting text from {file_path}")
            raw_text = self.extract_text(file_path)
            
            if not raw_text.strip():
                return DocumentParsingResult(
                    success=False,
                    error_message="No text could be extracted from the document",
                    raw_text=raw_text
                )
            
            
            logger.info("Parsing with LLM")
            invoice_data = self.parse_with_llm(raw_text)
            
            
            if invoice_data is None:
                logger.warning("LLM parsing failed, using fallback method")
                invoice_data = self.fallback_regex_extraction(raw_text)
            
            processing_time = time.time() - start_time
            
            return DocumentParsingResult(
                success=True,
                invoice_data=invoice_data,
                raw_text=raw_text,
                processing_time=processing_time,
                extraction_method="llm" if invoice_data else "regex"
            )
        
        except Exception as e:
            logger.error(f"Error parsing document: {e}")
            return DocumentParsingResult(
                success=False,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
    
    def batch_parse(self, file_paths: List[Union[str, Path]]) -> List[DocumentParsingResult]:
        results = []
        total_files = len(file_paths)

        for i, file_path in enumerate(file_paths, 1):
            logger.info(f"Processing {i}/{total_files}: {file_path}")
            result = self.parse_document(file_path)
            results.append(result)

            
            if result.success:
                logger.info(f"✓ Successfully processed {file_path}")
            else:
                logger.warning(f"✗ Failed to process {file_path}: {result.error_message}")

        return results

    def export_to_csv(self, results: List[DocumentParsingResult], output_path: Union[str, Path]) -> None:
        output_path = Path(output_path)
        csv_rows = []

        for result in results:
            if result.success and result.invoice_data:
                invoice_rows = result.invoice_data.to_csv_rows()
                csv_rows.extend(invoice_rows)
            else:
                
                csv_rows.append({
                    'invoice_number': '',
                    'invoice_date': '',
                    'vendor_name': '',
                    'customer_name': '',
                    'currency': '',
                    'subtotal': 0,
                    'tax_amount': 0,
                    'total': 0,
                    'item_name': f"ERROR: {result.error_message}",
                    'quantity': 0,
                    'unit_price': 0,
                    'item_total': 0
                })

        if csv_rows:
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=csv_rows[0].keys())
                writer.writeheader()
                writer.writerows(csv_rows)

            logger.info(f"Results exported to: {output_path}")
        else:
            logger.warning("No data to export")
