"""
Utility functions for OCR testing
"""
import cv2
import numpy as np
from PIL import Image, ImageEnhance
import logging
from pathlib import Path
import json
import time
from typing import Dict, List, Tuple, Any
import difflib
from config import LOGGING, OUTPUT_DIR

# Set up logging
logging.basicConfig(
    level=getattr(logging, LOGGING['level']),
    format=LOGGING['format'],
    handlers=[
        logging.FileHandler(LOGGING['file']),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def preprocess_image(image_path: str, config: Dict) -> np.ndarray:
    """
    Preprocess image for better OCR results
    
    Args:
        image_path: Path to the input image
        config: Preprocessing configuration
    
    Returns:
        Preprocessed image as numpy array
    """
    try:
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Could not load image: {image_path}")
        
        # Resize if needed
        if config.get('resize', False):
            height, width = image.shape[:2]
            max_width = config.get('max_width', 2000)
            max_height = config.get('max_height', 2000)
            
            if width > max_width or height > max_height:
                scale = min(max_width/width, max_height/height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
        
        # Convert to PIL for enhancement
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        
        # Enhance contrast
        if config.get('enhance_contrast', False):
            enhancer = ImageEnhance.Contrast(pil_image)
            pil_image = enhancer.enhance(1.2)
        
        # Convert back to OpenCV format
        image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        
        # Denoise
        if config.get('denoise', False):
            image = cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
        
        # Binarize (convert to black and white)
        if config.get('binarize', False):
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            _, image = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return image
        
    except Exception as e:
        logger.error(f"Error preprocessing image {image_path}: {str(e)}")
        raise

def calculate_accuracy_metrics(predicted_text: str, ground_truth: str = None) -> Dict[str, Any]:
    """
    Calculate various accuracy metrics for OCR results
    
    Args:
        predicted_text: Text extracted by OCR
        ground_truth: Expected text (if available)
    
    Returns:
        Dictionary containing accuracy metrics
    """
    metrics = {
        'character_count': len(predicted_text),
        'word_count': len(predicted_text.split()),
        'line_count': len(predicted_text.split('\n')),
        'has_content': len(predicted_text.strip()) > 0
    }
    
    if ground_truth:
        # Character-level accuracy
        char_accuracy = difflib.SequenceMatcher(None, predicted_text, ground_truth).ratio()
        metrics['character_accuracy'] = char_accuracy
        
        # Word-level accuracy
        pred_words = predicted_text.split()
        truth_words = ground_truth.split()
        word_accuracy = difflib.SequenceMatcher(None, pred_words, truth_words).ratio()
        metrics['word_accuracy'] = word_accuracy
        
        # Levenshtein distance
        try:
            import Levenshtein
            metrics['levenshtein_distance'] = Levenshtein.distance(predicted_text, ground_truth)
            metrics['normalized_levenshtein'] = metrics['levenshtein_distance'] / max(len(predicted_text), len(ground_truth))
        except ImportError:
            logger.warning("python-Levenshtein not installed, skipping Levenshtein distance calculation")
    
    return metrics

def save_results(results: Dict, output_path: Path, format_type: str = 'json'):
    """
    Save OCR results in specified format
    
    Args:
        results: Results dictionary
        output_path: Path to save results
        format_type: Format type ('json', 'text', 'csv')
    """
    try:
        if format_type == 'json':
            with open(output_path.with_suffix('.json'), 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
        
        elif format_type == 'text':
            with open(output_path.with_suffix('.txt'), 'w', encoding='utf-8') as f:
                if 'extracted_text' in results:
                    f.write(results['extracted_text'])
                else:
                    f.write(str(results))
        
        logger.info(f"Results saved to {output_path}")
        
    except Exception as e:
        logger.error(f"Error saving results: {str(e)}")
        raise

def create_comparison_report(all_results: List[Dict]) -> Dict:
    """
    Create a comprehensive comparison report of all OCR models
    
    Args:
        all_results: List of results from different OCR models
    
    Returns:
        Comparison report dictionary
    """
    report = {
        'summary': {
            'total_models_tested': len(all_results),
            'test_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        },
        'model_comparison': {},
        'recommendations': []
    }
    
    for result in all_results:
        model_name = result.get('model_name', 'unknown')
        report['model_comparison'][model_name] = {
            'processing_time': result.get('processing_time', 0),
            'confidence_score': result.get('confidence_score', 0),
            'text_length': len(result.get('extracted_text', '')),
            'word_count': len(result.get('extracted_text', '').split()),
            'metrics': result.get('metrics', {})
        }
    
    # Add recommendations based on results
    if all_results:
        fastest_model = min(all_results, key=lambda x: x.get('processing_time', float('inf')))
        report['recommendations'].append(f"Fastest model: {fastest_model.get('model_name', 'unknown')}")
        
        highest_confidence = max(all_results, key=lambda x: x.get('confidence_score', 0))
        report['recommendations'].append(f"Highest confidence: {highest_confidence.get('model_name', 'unknown')}")
    
    return report
