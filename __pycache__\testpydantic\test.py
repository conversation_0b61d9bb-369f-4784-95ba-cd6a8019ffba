import os
from groq import Groq
import instructor
from pydantic import BaseModel


client = Groq(api_key=os.getenv("GROQ_API_KEY"))
client = instructor.from_groq(client)

class User(BaseModel):
    name: str
    age: int
    year_of_birth: int
    birth_place: str

user = client.chat.completions.create(
    model="llama-3.3-70b-versatile",
    messages=[
        {"role": "user", "content": "Extract: my name is <PERSON><PERSON><PERSON><PERSON> and i am 20 years old and im a 2004 born , i was born in pondicherry"},
    ],
    response_model=User,
)

print(user)
