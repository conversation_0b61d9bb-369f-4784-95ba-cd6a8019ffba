#!/usr/bin/env python3
"""
Demo script showing the enhanced features of the invoice parser
"""

import os
import subprocess
from pathlib import Path

def run_command(cmd, description):
    """Run a command and show the description."""
    print(f"\n{'='*60}")
    print(f"🔍 {description}")
    print(f"{'='*60}")
    print(f"Command: {cmd}")
    print("-" * 60)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("Command timed out")
        return False
    except Exception as e:
        print(f"Error running command: {e}")
        return False

def main():
    """Demonstrate the enhanced invoice parser features."""
    
    print("🚀 Enhanced Invoice Parser Demo")
    print("=" * 60)
    print("This demo shows the new handwritten and image processing features")
    print("integrated into your existing invoice extractor.")
    
    # Check if we have test files
    test_files = []
    for ext in ['.pdf', '.png', '.jpg', '.jpeg']:
        test_files.extend(Path('.').glob(f'*{ext}'))
    
    if not test_files:
        print("\n⚠️  No test files found. Please add some invoice/receipt files to test.")
        print("Supported formats: PDF, PNG, JPG, JPEG, TIFF, BMP, WEBP")
        return
    
    test_file = test_files[0]
    print(f"\n📄 Using test file: {test_file}")
    
    # Demo 1: Standard processing
    run_command(
        f'python 2test.py -i "{test_file}" -o "demo_standard.csv" --save-json',
        "Standard Processing (Qwen2-VL + OCR)"
    )
    
    # Demo 2: Handwritten processing
    run_command(
        f'python 2test.py -i "{test_file}" -o "demo_handwritten.csv" --handwritten --save-json',
        "Handwritten Processing (Enhanced OCR + LLM Correction)"
    )
    
    # Demo 3: Image processing
    run_command(
        f'python 2test.py -i "{test_file}" -o "demo_image.csv" --img --save-json',
        "Image Processing (Qwen2-VL Vision Model)"
    )
    
    # Demo 4: OCR only
    run_command(
        f'python 2test.py -i "{test_file}" -o "demo_ocr.csv" --disable-vision --save-json',
        "OCR Only Processing (Tesseract)"
    )
    
    # Demo 5: Batch processing with handwritten mode
    if len(test_files) > 1:
        run_command(
            f'python 2test.py -i "." -o "demo_batch_handwritten.csv" --batch --handwritten --save-json',
            "Batch Handwritten Processing"
        )
    
    print(f"\n🎉 Demo Complete!")
    print("=" * 60)
    
    # Show generated files
    output_files = list(Path('.').glob('demo_*.csv'))
    json_files = list(Path('.').glob('demo_*.json'))
    
    if output_files:
        print("📊 Generated CSV files:")
        for file in output_files:
            print(f"  - {file}")
    
    if json_files:
        print("📋 Generated JSON files:")
        for file in json_files:
            print(f"  - {file}")
    
    print("\n💡 Usage Examples:")
    print("# Process handwritten receipt")
    print("python 2test.py -i handwritten_receipt.jpg -o results.csv --handwritten")
    print()
    print("# Process with maximum image accuracy")
    print("python 2test.py -i invoice.png -o results.csv --img")
    print()
    print("# Batch process handwritten documents")
    print("python 2test.py -i ./documents/ -o batch_results.csv --batch --handwritten")
    print()
    print("# Fast OCR-only processing")
    print("python 2test.py -i invoice.pdf -o results.csv --disable-vision")

if __name__ == "__main__":
    main()
