from datetime import datetime, date
from decimal import Decimal
from typing import List, Optional, Union, Any
from pydantic import BaseModel, Field, field_validator, model_validator
import re


class LineItem(BaseModel):
    name: str = Field(..., description="Item name or description", min_length=1)
    quantity: Optional[Union[int, float]] = Field(default=1, description="Quantity of items", ge=0)
    unit_price: Optional[float] = Field(default=None, description="Price per unit", ge=0)
    total_price: float = Field(..., description="Total price for this line item", ge=0)
    category: Optional[str] = Field(default=None, description="Item category")
    
    @field_validator('name')
    @classmethod
    def clean_name(cls, v):
        if not v or not v.strip():
            raise ValueError("Item name cannot be empty")
        
        cleaned = re.sub(r'\s+', ' ', v.strip())
        
        cleaned = re.sub(r'[^\w\s\-\.\,\(\)\&]', '', cleaned)
        return cleaned.title()

    @field_validator('total_price')
    @classmethod
    def validate_total_price(cls, v):
        if v is None or v < 0:
            raise ValueError("Total price must be non-negative")
        return round(float(v), 2)

    @model_validator(mode='after')
    def validate_pricing(self):
        quantity = self.quantity or 1
        unit_price = self.unit_price
        total_price = self.total_price

        if unit_price is not None and quantity is not None and total_price is not None:
            expected_total = round(float(unit_price) * float(quantity), 2)
            actual_total = round(float(total_price), 2)
            
            if abs(expected_total - actual_total) > 0.02:
                
                self.unit_price = round(total_price / quantity, 2) if quantity > 0 else total_price

        return self


class TaxInfo(BaseModel):
    tax_type: Optional[str] = Field(default=None, description="Type of tax (VAT, GST, Sales Tax, etc.)")
    tax_rate: Optional[float] = Field(default=None, description="Tax rate as percentage", ge=0, le=100)
    tax_amount: Optional[float] = Field(default=None, description="Tax amount", ge=0)


class ContactInfo(BaseModel):
    name: Optional[str] = Field(default=None, description="Name of person or company")
    address: Optional[str] = Field(default=None, description="Address")
    phone: Optional[str] = Field(default=None, description="Phone number")
    email: Optional[str] = Field(default=None, description="Email address")
    
    @field_validator('email')
    @classmethod
    def validate_email(cls, v):#for email
        if v and '@' not in v:
            return None  
        return v

    @field_validator('phone')
    @classmethod
    def clean_phone(cls, v):#for phone
        if v:
            
            cleaned = re.sub(r'[^\d\+\s\-\(\)]', '', v)
            return cleaned.strip() if cleaned else None
        return v


class InvoiceData(BaseModel):


    invoice_number: Optional[str] = Field(default=None, description="Invoice number")
    invoice_date: Optional[Union[datetime, date, str]] = Field(default=None, description="Invoice date")
    due_date: Optional[Union[datetime, date, str]] = Field(default=None, description="Payment due date")
    purchase_order_number: Optional[str] = Field(default=None, description="Purchase order number")


    vendor: Optional[ContactInfo] = Field(default=None, description="Vendor/seller information")
    customer: Optional[ContactInfo] = Field(default=None, description="Customer/buyer information")


    subtotal: Optional[float] = Field(default=None, description="Subtotal before tax", ge=0)
    tax_info: Optional[TaxInfo] = Field(default=None, description="Tax information")
    total: float = Field(..., description="Total amount", ge=0)
    currency: Optional[str] = Field(default="USD", description="Currency code")
    discount_amount: Optional[float] = Field(default=None, description="Discount amount", ge=0)
    shipping_amount: Optional[float] = Field(default=None, description="Shipping/delivery amount", ge=0)


    items: List[LineItem] = Field(default_factory=list, description="List of line items")


    document_type: Optional[str] = Field(default="invoice", description="Type of document")
    confidence_score: Optional[float] = Field(default=None, description="Extraction confidence", ge=0, le=1)
    payment_terms: Optional[str] = Field(default=None, description="Payment terms")
    payment_method: Optional[str] = Field(default=None, description="Payment method")
    notes: Optional[str] = Field(default=None, description="Additional notes or comments")
    other_information: Optional[str] = Field(default=None, description="Any other extracted information not fitting standard fields")
    
    @field_validator('invoice_date', 'due_date', mode='before')
    @classmethod
    def parse_date(cls, v):
        if v is None:
            return None

        if isinstance(v, (datetime, date)):
            return v

        if isinstance(v, str):
            
            date_formats = [
                '%Y-%m-%d',
                '%m/%d/%Y',
                '%d/%m/%Y',
                '%B %d, %Y',
                '%b %d, %Y',
                '%d %B %Y',
                '%d %b %Y',
                '%Y-%m-%d %H:%M:%S',
                '%m-%d-%Y',
                '%d-%m-%Y'
            ]

            
            v = re.sub(r'[^\w\s\-\/\,\:]', '', v.strip())

            for fmt in date_formats:
                try:
                    return datetime.strptime(v, fmt).date()
                except ValueError:
                    continue

            
            date_match = re.search(r'(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2,4})', v)
            if date_match:
                month, day, year = date_match.groups()
                year = int(year)
                if year < 100:
                    year += 2000 if year < 50 else 1900
                try:
                    return date(year, int(month), int(day))
                except ValueError:
                    
                    try:
                        return date(year, int(day), int(month))
                    except ValueError:
                        pass

        return None
    
    @field_validator('total')
    @classmethod
    def validate_total(cls, v):
        if v is None or v < 0:
            raise ValueError("Total amount must be non-negative")
        return round(float(v), 2)

    @field_validator('currency')
    @classmethod
    def validate_currency(cls, v):
        if v:
            v = v.upper().strip()
            
            valid_currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF', 'CNY', 'INR']
            if v in valid_currencies:
                return v
        return 'USD'  

    @model_validator(mode='after')
    def validate_financial_consistency(self):
        items = self.items or []
        total = self.total
        subtotal = self.subtotal
        tax_info = self.tax_info

        if items:
            
            calculated_subtotal = sum(item.total_price for item in items)

            if subtotal is None:
                self.subtotal = round(calculated_subtotal, 2)

            
            if tax_info and tax_info.tax_amount:
                expected_total = calculated_subtotal + tax_info.tax_amount
                if abs(expected_total - total) > 0.02:
                    
                    tax_info.tax_amount = round(total - calculated_subtotal, 2)

        return self
    
    def to_csv_rows(self) -> List[dict]:
        # Exact column order as specified by user
        from collections import OrderedDict
        base_info = OrderedDict([
            ('invoice_number', self.invoice_number or ''),
            ('invoice_date', str(self.invoice_date) if self.invoice_date else ''),
            ('due_date', str(self.due_date) if self.due_date else ''),
            ('purchase_order_number', self.purchase_order_number or ''),
            ('vendor_name', self.vendor.name if self.vendor else ''),
            ('vendor_address', self.vendor.address if self.vendor else ''),
            ('vendor_phone', self.vendor.phone if self.vendor else ''),
            ('vendor_email', self.vendor.email if self.vendor else ''),
            ('customer_name', self.customer.name if self.customer else ''),
            ('customer_address', self.customer.address if self.customer else ''),
            ('customer_phone', self.customer.phone if self.customer else ''),
            ('customer_email', self.customer.email if self.customer else ''),
            ('currency', self.currency),
            ('subtotal', self.subtotal or 0),
            ('tax_type', self.tax_info.tax_type if self.tax_info else ''),
            ('tax_rate', self.tax_info.tax_rate if self.tax_info else 0),
            ('tax_amount', self.tax_info.tax_amount if self.tax_info else 0),
            ('discount_amount', self.discount_amount or 0),
            ('shipping_amount', self.shipping_amount or 0),
            ('total', self.total),
            ('payment_terms', self.payment_terms or ''),
            ('payment_method', self.payment_method or ''),
            ('notes', self.notes or ''),
            ('other_information', self.other_information or ''),
            ('item_name', ''),
            ('item_category', ''),
            ('quantity', 0),
            ('unit_price', 0),
            ('item_total', 0)
        ])

        if not self.items:
            # Return single row with empty item fields
            return [base_info]

        # Create rows for each item, maintaining exact column order
        rows = []
        for item in self.items:
            row = base_info.copy()
            row['item_name'] = item.name
            row['item_category'] = item.category or ''
            row['quantity'] = item.quantity or 1
            row['unit_price'] = item.unit_price or 0
            row['item_total'] = item.total_price
            rows.append(row)

        return rows


class DocumentParsingResult(BaseModel):
    success: bool = Field(..., description="Whether parsing was successful")
    invoice_data: Optional[InvoiceData] = Field(default=None, description="Parsed invoice data")
    error_message: Optional[str] = Field(default=None, description="Error message if parsing failed")
    raw_text: Optional[str] = Field(default=None, description="Raw extracted text")
    processing_time: Optional[float] = Field(default=None, description="Processing time in seconds")
    extraction_method: Optional[str] = Field(default=None, description="Method used for extraction")
