#!/usr/bin/env python3
"""
Debug script to test OCR on images and help troubleshoot extraction issues
"""

import os
import sys
from pathlib import Path
import pytesseract
from PIL import Image
import cv2
import numpy as np

def test_basic_ocr(image_path):
    """Test basic OCR configurations on an image."""
    print(f"\n🔍 Testing OCR on: {image_path}")
    print("=" * 60)
    
    try:
        img = Image.open(image_path)
        print(f"Image size: {img.size}")
        print(f"Image mode: {img.mode}")
        
        # Test different OCR configurations
        configs = [
            ("Default", ""),
            ("PSM 6", "--psm 6"),
            ("PSM 7", "--psm 7"), 
            ("PSM 8", "--psm 8"),
            ("PSM 11", "--psm 11"),
            ("PSM 13", "--psm 13"),
            ("OEM 3 PSM 6", "--oem 3 --psm 6"),
            ("Legacy OEM 0", "--oem 0 --psm 6"),
            ("LSTM OEM 1", "--oem 1 --psm 6")
        ]
        
        results = []
        
        for name, config in configs:
            try:
                text = pytesseract.image_to_string(img, config=config)
                text_clean = text.strip()
                char_count = len(text_clean)
                
                if char_count > 0:
                    results.append((name, char_count, text_clean[:100]))
                    print(f"✓ {name:15} | {char_count:3d} chars | {text_clean[:50]}...")
                else:
                    print(f"✗ {name:15} | No text found")
                    
            except Exception as e:
                print(f"✗ {name:15} | Error: {e}")
        
        if results:
            # Show best result
            best = max(results, key=lambda x: x[1])
            print(f"\n🏆 Best result: {best[0]} ({best[1]} characters)")
            print(f"Text preview: {best[2]}")
        else:
            print("\n❌ No OCR configuration found any text")
            
        return results
        
    except Exception as e:
        print(f"❌ Error processing image: {e}")
        return []

def test_preprocessing(image_path):
    """Test different image preprocessing approaches."""
    print(f"\n🖼️  Testing preprocessing on: {image_path}")
    print("=" * 60)
    
    try:
        img = Image.open(image_path)
        img_array = np.array(img)
        
        # Convert to grayscale if needed
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        else:
            gray = img_array
        
        preprocessing_methods = [
            ("Original", img),
            ("Grayscale", Image.fromarray(gray)),
        ]
        
        # Additional preprocessing if OpenCV is available
        try:
            # Adaptive threshold
            thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
            preprocessing_methods.append(("Adaptive Threshold", Image.fromarray(thresh)))
            
            # OTSU threshold
            _, otsu = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            preprocessing_methods.append(("OTSU Threshold", Image.fromarray(otsu)))
            
            # High contrast
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)
            preprocessing_methods.append(("CLAHE Enhanced", Image.fromarray(enhanced)))
            
        except Exception as e:
            print(f"⚠️  OpenCV preprocessing failed: {e}")
        
        best_results = []
        
        for method_name, processed_img in preprocessing_methods:
            try:
                # Test with PSM 6 (most common)
                text = pytesseract.image_to_string(processed_img, config="--oem 3 --psm 6")
                char_count = len(text.strip())
                
                if char_count > 0:
                    best_results.append((method_name, char_count, text.strip()[:100]))
                    print(f"✓ {method_name:20} | {char_count:3d} chars | {text.strip()[:50]}...")
                else:
                    print(f"✗ {method_name:20} | No text found")
                    
            except Exception as e:
                print(f"✗ {method_name:20} | Error: {e}")
        
        if best_results:
            best = max(best_results, key=lambda x: x[1])
            print(f"\n🏆 Best preprocessing: {best[0]} ({best[1]} characters)")
        
        return best_results
        
    except Exception as e:
        print(f"❌ Error in preprocessing test: {e}")
        return []

def main():
    """Main debug function."""
    print("🔧 OCR Debug Tool")
    print("=" * 60)
    
    # Check if image file is provided
    if len(sys.argv) > 1:
        image_path = sys.argv[1]
    else:
        # Look for image files in current directory
        image_files = []
        for ext in ['.png', '.jpg', '.jpeg', '.tiff', '.bmp']:
            image_files.extend(Path('.').glob(f'*{ext}'))
        
        if not image_files:
            print("❌ No image files found. Usage: python debug_ocr.py <image_file>")
            return
        
        image_path = image_files[0]
        print(f"📁 Using first found image: {image_path}")
    
    if not Path(image_path).exists():
        print(f"❌ Image file not found: {image_path}")
        return
    
    # Test basic OCR
    ocr_results = test_basic_ocr(image_path)
    
    # Test preprocessing
    preprocessing_results = test_preprocessing(image_path)
    
    # Summary
    print(f"\n📊 Summary for {image_path}")
    print("=" * 60)
    
    if ocr_results:
        best_ocr = max(ocr_results, key=lambda x: x[1])
        print(f"Best OCR config: {best_ocr[0]} ({best_ocr[1]} chars)")
    
    if preprocessing_results:
        best_prep = max(preprocessing_results, key=lambda x: x[1])
        print(f"Best preprocessing: {best_prep[0]} ({best_prep[1]} chars)")
    
    if not ocr_results and not preprocessing_results:
        print("❌ No text could be extracted with any method")
        print("\n💡 Suggestions:")
        print("- Check if image contains readable text")
        print("- Try higher resolution image (300+ DPI)")
        print("- Ensure good contrast between text and background")
        print("- Make sure text is not too small or blurry")
    else:
        print("\n💡 For handwritten documents, try:")
        print("python 2test.py -i your_image.png -o results.csv --handwritten")

if __name__ == "__main__":
    main()
