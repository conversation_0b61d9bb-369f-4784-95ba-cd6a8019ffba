"""
Advanced metrics and analysis tools for OCR results
"""
import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Any
import numpy as np
from collections import defaultdict
import logging

logger = logging.getLogger(__name__)

class OCRMetricsAnalyzer:
    """Advanced analysis and visualization of OCR results"""
    
    def __init__(self, results_file: str = None, results_data: List[Dict] = None):
        """
        Initialize analyzer with results data
        
        Args:
            results_file: Path to JSON results file
            results_data: Direct results data
        """
        if results_file:
            with open(results_file, 'r', encoding='utf-8') as f:
                self.results = json.load(f)
        elif results_data:
            self.results = results_data
        else:
            raise ValueError("Either results_file or results_data must be provided")
        
        self.df = self._create_dataframe()
    
    def _create_dataframe(self) -> pd.DataFrame:
        """Convert results to pandas DataFrame for analysis"""
        rows = []
        
        for result in self.results:
            row = {
                'image_name': Path(result['image_path']).name,
                'model_name': result['model_name'],
                'success': result['success'],
                'processing_time': result.get('processing_time', 0),
                'confidence_score': result.get('confidence_score', 0),
                'text_length': len(result.get('extracted_text', '')),
                'word_count': len(result.get('extracted_text', '').split()),
                'extracted_text': result.get('extracted_text', ''),
                'error': result.get('error', '')
            }
            
            # Add metrics if available
            if 'metrics' in result:
                for metric_name, metric_value in result['metrics'].items():
                    row[f'metric_{metric_name}'] = metric_value
            
            # Add ground truth comparison if available
            if 'ground_truth' in result:
                row['has_ground_truth'] = True
                row['ground_truth'] = result['ground_truth']
            else:
                row['has_ground_truth'] = False
            
            rows.append(row)
        
        return pd.DataFrame(rows)
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance analysis"""
        report = {
            'overall_stats': self._get_overall_stats(),
            'model_comparison': self._get_model_comparison(),
            'accuracy_analysis': self._get_accuracy_analysis(),
            'performance_analysis': self._get_performance_analysis(),
            'recommendations': self._get_recommendations()
        }
        
        return report
    
    def _get_overall_stats(self) -> Dict[str, Any]:
        """Get overall statistics"""
        total_tests = len(self.df)
        successful_tests = len(self.df[self.df['success'] == True])
        unique_images = self.df['image_name'].nunique()
        unique_models = self.df['model_name'].nunique()
        
        return {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'success_rate': successful_tests / total_tests if total_tests > 0 else 0,
            'unique_images': unique_images,
            'unique_models': unique_models,
            'average_processing_time': self.df['processing_time'].mean(),
            'average_confidence': self.df['confidence_score'].mean(),
            'average_text_length': self.df['text_length'].mean()
        }
    
    def _get_model_comparison(self) -> Dict[str, Dict]:
        """Compare performance across models"""
        model_stats = {}
        
        for model in self.df['model_name'].unique():
            model_data = self.df[self.df['model_name'] == model]
            successful_data = model_data[model_data['success'] == True]
            
            stats = {
                'total_tests': len(model_data),
                'successful_tests': len(successful_data),
                'success_rate': len(successful_data) / len(model_data) if len(model_data) > 0 else 0,
                'avg_processing_time': successful_data['processing_time'].mean() if len(successful_data) > 0 else 0,
                'avg_confidence': successful_data['confidence_score'].mean() if len(successful_data) > 0 else 0,
                'avg_text_length': successful_data['text_length'].mean() if len(successful_data) > 0 else 0,
                'avg_word_count': successful_data['word_count'].mean() if len(successful_data) > 0 else 0
            }
            
            # Add accuracy metrics if available
            accuracy_cols = [col for col in self.df.columns if col.startswith('metric_') and 'accuracy' in col]
            for col in accuracy_cols:
                if col in successful_data.columns:
                    stats[col.replace('metric_', 'avg_')] = successful_data[col].mean()
            
            model_stats[model] = stats
        
        return model_stats
    
    def _get_accuracy_analysis(self) -> Dict[str, Any]:
        """Analyze accuracy metrics if ground truth is available"""
        if not self.df['has_ground_truth'].any():
            return {'message': 'No ground truth data available for accuracy analysis'}
        
        gt_data = self.df[self.df['has_ground_truth'] == True]
        
        analysis = {
            'images_with_ground_truth': len(gt_data['image_name'].unique()),
            'total_comparisons': len(gt_data)
        }
        
        # Analyze accuracy metrics by model
        accuracy_cols = [col for col in gt_data.columns if col.startswith('metric_') and 'accuracy' in col]
        
        if accuracy_cols:
            model_accuracy = {}
            for model in gt_data['model_name'].unique():
                model_data = gt_data[gt_data['model_name'] == model]
                model_accuracy[model] = {}
                
                for col in accuracy_cols:
                    if col in model_data.columns:
                        metric_name = col.replace('metric_', '')
                        model_accuracy[model][metric_name] = {
                            'mean': model_data[col].mean(),
                            'std': model_data[col].std(),
                            'min': model_data[col].min(),
                            'max': model_data[col].max()
                        }
            
            analysis['model_accuracy'] = model_accuracy
        
        return analysis
    
    def _get_performance_analysis(self) -> Dict[str, Any]:
        """Analyze performance characteristics"""
        successful_data = self.df[self.df['success'] == True]
        
        if len(successful_data) == 0:
            return {'message': 'No successful tests for performance analysis'}
        
        # Speed analysis
        speed_stats = {
            'fastest_model': successful_data.loc[successful_data['processing_time'].idxmin(), 'model_name'],
            'slowest_model': successful_data.loc[successful_data['processing_time'].idxmax(), 'model_name'],
            'speed_by_model': successful_data.groupby('model_name')['processing_time'].agg(['mean', 'std', 'min', 'max']).to_dict()
        }
        
        # Confidence analysis
        confidence_stats = {
            'highest_confidence_model': successful_data.loc[successful_data['confidence_score'].idxmax(), 'model_name'],
            'confidence_by_model': successful_data.groupby('model_name')['confidence_score'].agg(['mean', 'std', 'min', 'max']).to_dict()
        }
        
        # Text extraction analysis
        text_stats = {
            'most_text_extracted': successful_data.loc[successful_data['text_length'].idxmax(), 'model_name'],
            'text_length_by_model': successful_data.groupby('model_name')['text_length'].agg(['mean', 'std', 'min', 'max']).to_dict()
        }
        
        return {
            'speed_analysis': speed_stats,
            'confidence_analysis': confidence_stats,
            'text_extraction_analysis': text_stats
        }
    
    def _get_recommendations(self) -> List[str]:
        """Generate recommendations based on analysis"""
        recommendations = []
        successful_data = self.df[self.df['success'] == True]
        
        if len(successful_data) == 0:
            recommendations.append("No successful tests found. Check model configurations and input images.")
            return recommendations
        
        # Speed recommendation
        fastest_model = successful_data.groupby('model_name')['processing_time'].mean().idxmin()
        recommendations.append(f"For fastest processing: Use {fastest_model}")
        
        # Confidence recommendation
        highest_conf_model = successful_data.groupby('model_name')['confidence_score'].mean().idxmax()
        recommendations.append(f"For highest confidence scores: Use {highest_conf_model}")
        
        # Success rate recommendation
        success_rates = self.df.groupby('model_name')['success'].mean()
        most_reliable = success_rates.idxmax()
        recommendations.append(f"For most reliable results: Use {most_reliable} (success rate: {success_rates[most_reliable]:.1%})")
        
        # Accuracy recommendation (if ground truth available)
        if self.df['has_ground_truth'].any():
            gt_data = self.df[self.df['has_ground_truth'] == True]
            accuracy_cols = [col for col in gt_data.columns if col.startswith('metric_') and 'accuracy' in col]
            
            if accuracy_cols:
                best_accuracy_model = gt_data.groupby('model_name')[accuracy_cols[0]].mean().idxmax()
                recommendations.append(f"For best accuracy: Use {best_accuracy_model}")
        
        return recommendations
    
    def create_visualizations(self, output_dir: str):
        """Create visualization plots"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # Set style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # 1. Success rate by model
        self._plot_success_rates(output_path)
        
        # 2. Processing time comparison
        self._plot_processing_times(output_path)
        
        # 3. Confidence scores
        self._plot_confidence_scores(output_path)
        
        # 4. Text length distribution
        self._plot_text_lengths(output_path)
        
        # 5. Accuracy comparison (if available)
        if self.df['has_ground_truth'].any():
            self._plot_accuracy_metrics(output_path)
        
        logger.info(f"Visualizations saved to {output_path}")
    
    def _plot_success_rates(self, output_path: Path):
        """Plot success rates by model"""
        success_rates = self.df.groupby('model_name')['success'].mean()
        
        plt.figure(figsize=(10, 6))
        bars = plt.bar(success_rates.index, success_rates.values)
        plt.title('Success Rate by OCR Model')
        plt.xlabel('Model')
        plt.ylabel('Success Rate')
        plt.ylim(0, 1)
        
        # Add value labels on bars
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.1%}', ha='center', va='bottom')
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(output_path / 'success_rates.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_processing_times(self, output_path: Path):
        """Plot processing time comparison"""
        successful_data = self.df[self.df['success'] == True]
        
        if len(successful_data) == 0:
            return
        
        plt.figure(figsize=(12, 6))
        sns.boxplot(data=successful_data, x='model_name', y='processing_time')
        plt.title('Processing Time Distribution by Model')
        plt.xlabel('Model')
        plt.ylabel('Processing Time (seconds)')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(output_path / 'processing_times.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_confidence_scores(self, output_path: Path):
        """Plot confidence score distributions"""
        successful_data = self.df[self.df['success'] == True]
        
        if len(successful_data) == 0:
            return
        
        plt.figure(figsize=(12, 6))
        sns.boxplot(data=successful_data, x='model_name', y='confidence_score')
        plt.title('Confidence Score Distribution by Model')
        plt.xlabel('Model')
        plt.ylabel('Confidence Score')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(output_path / 'confidence_scores.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_text_lengths(self, output_path: Path):
        """Plot text length distributions"""
        successful_data = self.df[self.df['success'] == True]
        
        if len(successful_data) == 0:
            return
        
        plt.figure(figsize=(12, 6))
        sns.boxplot(data=successful_data, x='model_name', y='text_length')
        plt.title('Extracted Text Length Distribution by Model')
        plt.xlabel('Model')
        plt.ylabel('Text Length (characters)')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(output_path / 'text_lengths.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_accuracy_metrics(self, output_path: Path):
        """Plot accuracy metrics if available"""
        gt_data = self.df[self.df['has_ground_truth'] == True]
        accuracy_cols = [col for col in gt_data.columns if col.startswith('metric_') and 'accuracy' in col]
        
        if not accuracy_cols:
            return
        
        for col in accuracy_cols:
            metric_name = col.replace('metric_', '').replace('_', ' ').title()
            
            plt.figure(figsize=(10, 6))
            sns.boxplot(data=gt_data, x='model_name', y=col)
            plt.title(f'{metric_name} by Model')
            plt.xlabel('Model')
            plt.ylabel(metric_name)
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            filename = col.replace('metric_', '') + '.png'
            plt.savefig(output_path / filename, dpi=300, bbox_inches='tight')
            plt.close()
    
    def save_report(self, output_file: str):
        """Save comprehensive analysis report"""
        report = self.generate_performance_report()
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Analysis report saved to {output_file}")
        return report
