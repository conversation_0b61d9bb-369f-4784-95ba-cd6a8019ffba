from pathlib import Path
import difflib
import re
import json
import csv
import tempfile
from datetime import datetime
from io import BytesIO

try:
    from PIL import Image
    from pdf2image import convert_from_path
    import torch
    IMAGE_PROCESSING_AVAILABLE = True
except ImportError:
    IMAGE_PROCESSING_AVAILABLE = False
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import (
    PdfPipelineOptions,
    TesseractCliOcrOptions,
)
from docling.document_converter import DocumentConverter, PdfFormatOption

# SmolDocling imports (Hugging Face transformers)
try:
    from transformers import AutoProcessor, AutoModelForImageTextToText
    import torch
    SMOLDOCLING_AVAILABLE = True
except ImportError:
    print("SmolDocling not available, using enhanced pattern matching...")
    SMOLDOCLING_AVAILABLE = False
ocr_options = TesseractCliOcrOptions(
    lang=["auto"],
    tesseract_cmd=r"C:\Users\<USER>\Documents\Intern_2025\ArunKumarV(2951795)\Tesseractengine\tesseract.exe"
)

def load_smoldocling_model():
    """Load the SmolDocling model for high-accuracy document understanding."""
    if not SMOLDOCLING_AVAILABLE:
        print("SmolDocling not available, using enhanced pattern matching...")
        return None, None

    try:
        print("Loading SmolDocling model from Hugging Face...")

        # Load model and processor from Hugging Face with timeout handling
        model_name = "ds4sd/SmolDocling-256M-preview"

        # Set longer timeout for model download
        import socket
        original_timeout = socket.getdefaulttimeout()
        socket.setdefaulttimeout(60)  # 60 seconds timeout

        try:
            processor = AutoProcessor.from_pretrained(
                model_name,
                cache_dir=None,  # Use default cache
                resume_download=True  # Resume if interrupted
            )
            model = AutoModelForImageTextToText.from_pretrained(
                model_name,
                cache_dir=None,
                resume_download=True,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
            )
        finally:
            socket.setdefaulttimeout(original_timeout)

        # Set device (GPU if available, otherwise CPU)
        device = "cuda" if torch.cuda.is_available() else "cpu"
        model = model.to(device)
        model.eval()

        print(f"SmolDocling model loaded successfully on {device}")
        print(f"Model dtype: {next(model.parameters()).dtype}")
        return model, processor
    except Exception as e:
        print(f"Warning: Could not load SmolDocling model: {e}")
        print("This is likely due to network timeout or model size.")
        print("Falling back to enhanced pattern matching...")
        return None, None

def extract_structured_data_with_smoldocling(image, model, processor):
    """Extract structured data using SmolDocling for high accuracy."""
    if not SMOLDOCLING_AVAILABLE:
        return None

    try:
        # Create a comprehensive prompt for invoice/document extraction
        prompt = """<|im_start|>user
Analyze this invoice/document image and extract the following information in JSON format:

{
    "buyer": "company or person receiving the invoice/goods",
    "sender": "company or person sending the invoice/providing goods",
    "invoice_date": "date of the invoice",
    "items": [
        {
            "description": "item description",
            "price": "item price (numbers only)"
        }
    ]
}

Look for:
- Buyer information: customer name, bill to, ship to, buyer details
- Sender information: company name, vendor, seller, from address
- Invoice date: any date mentioned in the document
- Items: all products, services, fees, charges with their prices

Extract ALL items including vehicles, fees, services, taxes, etc. Be thorough and accurate.
Return ONLY the JSON object, no other text.<|im_end|>
<|im_start|>assistant"""

        # Process the image and prompt
        device = next(model.parameters()).device
        inputs = processor(text=prompt, images=image, return_tensors="pt").to(device)

        # Generate response
        with torch.no_grad():
            generated_ids = model.generate(
                **inputs,
                max_new_tokens=1024,
                do_sample=False,
                temperature=0.1,
                pad_token_id=processor.tokenizer.eos_token_id
            )

        # Decode the response
        generated_text = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]

        # Extract JSON from the response
        try:
            # Find the assistant's response after the prompt
            assistant_start = generated_text.find("<|im_start|>assistant") + len("<|im_start|>assistant")
            response_text = generated_text[assistant_start:].strip()

            # Find JSON in the response
            start_idx = response_text.find("{")
            end_idx = response_text.rfind("}") + 1

            if start_idx != -1 and end_idx > start_idx:
                json_str = response_text[start_idx:end_idx]
                parsed_data = json.loads(json_str)

                # Validate and clean the extracted data
                return validate_and_clean_extracted_data(parsed_data)
        except json.JSONDecodeError as e:
            print(f"JSON parsing error: {e}")
            print(f"Raw response: {response_text[:200]}...")
        except Exception as e:
            print(f"Response processing error: {e}")

        return None
    except Exception as e:
        print(f"SmolDocling extraction failed: {e}")
        return None

def validate_and_clean_extracted_data(data):
    """Validate and clean extracted data from SmolDocling."""
    if not isinstance(data, dict):
        return None

    # Ensure required fields exist
    cleaned_data = {
        "buyer": str(data.get("buyer", "")).strip(),
        "sender": str(data.get("sender", "")).strip(),
        "invoice_date": str(data.get("invoice_date", "")).strip(),
        "items": []
    }

    # Clean and validate items
    items = data.get("items", [])
    if isinstance(items, list):
        for item in items:
            if isinstance(item, dict):
                description = str(item.get("description", "")).strip()
                price = str(item.get("price", "")).strip()

                # Clean price - extract numbers only
                price_clean = re.sub(r'[^\d.,]', '', price)

                if description and price_clean:
                    cleaned_data["items"].append({
                        "description": description,
                        "price": price_clean
                    })

    return cleaned_data

def test_extraction_patterns():
    """Test the enhanced extraction patterns with various invoice formats."""
    test_cases = [
        # Test case 1: Standard invoice format
        """
        INVOICE #12345
        Date: March 15, 2024
        Bill To: John Smith
        From: ABC Company Inc.

        Item 1: Laptop Computer - $1,200.00
        Item 2: Software License - $300.00
        Tax: $150.00
        Total: $1,650.00
        """,

        # Test case 2: Table format
        """
        INVOICE
        Date: 2024-03-15
        Customer: Jane Doe
        Vendor: XYZ Corp

        | Description | Amount |
        |-------------|--------|
        | Consulting Services | 2500.00 |
        | Travel Expenses | 450.00 |
        | Materials | 150.00 |
        """,

        # Test case 3: Different date format
        """
        Invoice Number: INV-2024-001
        Issued: 15/03/2024
        Client: Tech Solutions Ltd
        Supplier: Digital Services Co

        Web Development: $5,000
        Hosting Setup: $200
        Domain Registration: $15
        """
    ]

    print("\n" + "="*50)
    print("TESTING ENHANCED EXTRACTION PATTERNS")
    print("="*50)

    for i, test_text in enumerate(test_cases, 1):
        print(f"\nTest Case {i}:")
        print("-" * 20)
        result = extract_structured_data_from_text(test_text)
        print(f"Buyer: {result['buyer']}")
        print(f"Sender: {result['sender']}")
        print(f"Date: {result['invoice_date']}")
        print(f"Items: {len(result['items'])}")
        for j, item in enumerate(result['items'], 1):
            print(f"  {j}. {item['description']} - ${item['price']}")

def extract_structured_data_from_text(text):
    """Extract structured data from plain text using enhanced pattern matching."""
    data = {
        "buyer": "",
        "sender": "",
        "invoice_date": "",
        "items": []
    }

    lines = text.split('\n')
    text_lower = text.lower()

    # Enhanced date extraction with more patterns for different invoice types
    date_patterns = [
        r'(?:invoice\s+)?date:\s*([a-z]+ \d{1,2}, \d{4})',
        r'(?:invoice\s+)?date:\s*(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
        r'(?:invoice\s+)?date:\s*(\d{4}[/-]\d{1,2}[/-]\d{1,2})',
        r'(?:date|dated):\s*(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
        r'(?:date|dated):\s*(\d{4}[/-]\d{1,2}[/-]\d{1,2})',
        r'\b((?:january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2},?\s+\d{4})\b',
        r'\b(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})\b',
        r'\b(\d{4}[/-]\d{1,2}[/-]\d{1,2})\b',
        r'(?:issued|created|generated).*?(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
        r'(?:issued|created|generated).*?((?:january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2},?\s+\d{4})'
    ]

    for pattern in date_patterns:
        date_match = re.search(pattern, text_lower)
        if date_match:
            data["invoice_date"] = date_match.group(1)
            break

    # Enhanced buyer extraction for multiple invoice types
    buyer_patterns = [
        r'(?:bill\s+to|buyer|customer|client|sold\s+to|ship\s+to|invoice\s+to):\s*([^\n\r]+)',
        r'(?:buyer|customer):\s*([^\n\r]+)',
        r'(?:bill\s+to|ship\s+to):\s*([^\n\r]+)',
        r'(?:client|customer)\s+name:\s*([^\n\r]+)',
        r'(?:purchaser|recipient):\s*([^\n\r]+)',
        r'(?:billed\s+to|invoiced\s+to):\s*([^\n\r]+)',
        r'(?:pay\s+to|payment\s+to):\s*([^\n\r]+)',
        r'(?:account\s+holder|account\s+name):\s*([^\n\r]+)',
        r'(?:company|organization|business):\s*([^\n\r]+)',
        r'(?:name|full\s+name):\s*([A-Za-z\s]{3,50})',
        r'(?:to|for):\s*([A-Za-z\s]{5,50})',
        r'(?:mr\.|mrs\.|ms\.|dr\.)\s*([A-Za-z\s]{3,50})'
    ]

    # Try pattern-based extraction first
    for pattern in buyer_patterns:
        buyer_match = re.search(pattern, text_lower)
        if buyer_match:
            buyer_candidate = buyer_match.group(1).strip()
            if len(buyer_candidate) > 2 and not any(skip in buyer_candidate for skip in ['bank', 'address', 'account']):
                data["buyer"] = buyer_candidate
                break

    # Fallback to section-based extraction
    if not data["buyer"]:
        buyer_section_found = False
        for i, line in enumerate(lines):
            line_clean = line.strip()
            line_lower = line_clean.lower()

            if any(keyword in line_lower for keyword in ['buyer:', '## buyer:', 'bill to:', 'customer:', 'client:']):
                buyer_section_found = True
                continue

            if buyer_section_found and line_clean:
                if not any(skip in line_lower for skip in ['bank', 'address', 'swift', 'aba', 'account', 'beneficiary']):
                    if 'lot:' in line_lower:
                        for j in range(i + 1, min(i + 5, len(lines))):
                            next_line = lines[j].strip()
                            if next_line and not next_line.isdigit() and len(next_line) > 3:
                                if re.match(r'^[A-Za-z\s]+$', next_line) or ('passport' in lines[j+1].lower() if j+1 < len(lines) else False):
                                    data["buyer"] = next_line
                                    break
                        break
                    elif len(line_clean) > 3 and re.match(r'^[A-Za-z\s]+$', line_clean):
                        data["buyer"] = line_clean
                        break

    # Enhanced sender extraction for multiple invoice types
    sender_patterns = [
        r'(?:from|seller|vendor|company|supplier|invoice\s+from):\s*([^\n\r]+)',
        r'(?:beneficiary\s+name):\s*([^,\n\r]+)',
        r'(?:company|corporation|business)\s+name:\s*([^\n\r]+)',
        r'(?:vendor|supplier|provider):\s*([^\n\r]+)',
        r'(?:issued\s+by|sent\s+by|billed\s+by):\s*([^\n\r]+)',
        r'(?:service\s+provider|contractor):\s*([^\n\r]+)',
        r'(?:merchant|retailer|store):\s*([^\n\r]+)',
        r'(?:business|firm|enterprise):\s*([^\n\r]+)',
        r'(?:remit\s+to|pay\s+to):\s*([^\n\r]+)',
        r'^([A-Z][A-Za-z\s&,\.]{5,50}(?:Inc|Corp|LLC|Ltd|Co|Company|Corporation|Limited)\.?)$',
        r'([A-Z][A-Za-z\s&,\.]{3,40}(?:Inc|Corp|LLC|Ltd|Co|Company|Corporation|Limited))',
        r'(?:invoice\s+#|invoice\s+number|inv\s+#).*?(?:from|by)\s*([^\n\r]+)'
    ]

    # Try pattern-based extraction first
    for pattern in sender_patterns:
        sender_match = re.search(pattern, text_lower)
        if sender_match:
            sender_candidate = sender_match.group(1).strip()
            if len(sender_candidate) > 2:
                data["sender"] = sender_candidate
                break

    # Fallback to line-by-line search
    if not data["sender"]:
        for i, line in enumerate(lines):
            line_clean = line.strip()
            line_lower = line_clean.lower()

            if 'beneficiary name:' in line_lower:
                beneficiary_match = re.search(r'beneficiary name:\s*([^,\n]+)', line_lower)
                if beneficiary_match:
                    data["sender"] = beneficiary_match.group(1).strip()
                    break

            # Look for company indicators in first few lines
            if i < 5 and any(indicator in line_lower for indicator in ['inc', 'corp', 'company', 'co.', 'llc', 'ltd']):
                data["sender"] = line_clean
                break

    # Enhanced items extraction for multiple table formats and line items

    # Method 1: Extract from markdown-style tables
    in_table = False
    table_headers_found = False

    for i, line in enumerate(lines):
        line_clean = line.strip()

        # Detect various table header formats
        if any(header in line.upper() for header in ['| DESCRIPTION', 'DESCRIPTION', 'ITEM', 'PRODUCT', 'SERVICE']):
            if any(price_header in line.upper() for price_header in ['TOTAL', 'PRICE', 'AMOUNT', 'COST']):
                in_table = True
                table_headers_found = True
                continue

        # Skip table separators
        if in_table and line_clean.startswith('|---'):
            continue

        # Extract from table rows
        if in_table and line_clean.startswith('|') and line_clean.endswith('|'):
            parts = [part.strip() for part in line_clean.split('|')]
            if len(parts) >= 3:
                description = parts[1].strip()
                price_str = parts[2].strip()

                if description and description != '' and not description.startswith('$'):
                    price_match = re.search(r'(\d+(?:,\d{3})*(?:\.\d{2})?)', price_str)
                    if price_match:
                        price = price_match.group(1)
                        if description.lower() not in ['total', 'subtotal', 'grand total', 'tax', 'shipping']:
                            data["items"].append({
                                "description": description,
                                "price": price
                            })

    # Method 2: Extract from non-table formats (line items with prices)
    if not data["items"]:
        # Enhanced price patterns for better accuracy
        price_patterns = [
            # Standard currency formats
            r'(.+?)\s+[\$₹€£¥]\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',
            r'(.+?)\s+(\d+(?:,\d{3})*(?:\.\d{2})?)\s*[\$₹€£¥]',

            # Currency codes
            r'(.+?)\s+(?:USD|EUR|GBP|INR|CAD|AUD)\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',
            r'(.+?)\s+(\d+(?:,\d{3})*(?:\.\d{2})?)\s+(?:USD|EUR|GBP|INR|CAD|AUD)',

            # Dash/colon separators
            r'(.+?)\s*[-:]\s*[\$₹€£¥]?\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',
            r'(.+?)\s*[-:]\s*(\d+(?:,\d{3})*(?:\.\d{2})?)\s*[\$₹€£¥]?',

            # Tab separated
            r'(.+?)\t+[\$₹€£¥]?\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',
            r'(.+?)\t+(\d+(?:,\d{3})*(?:\.\d{2})?)\s*[\$₹€£¥]?',

            # Multiple spaces
            r'(.+?)\s{3,}[\$₹€£¥]?\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',
            r'(.+?)\s{3,}(\d+(?:,\d{3})*(?:\.\d{2})?)\s*[\$₹€£¥]?',

            # Parentheses
            r'(.+?)\s*\(\s*[\$₹€£¥]?\s*(\d+(?:,\d{3})*(?:\.\d{2})?)\s*\)',

            # At end of line
            r'(.+?)\s+(\d+(?:,\d{3})*(?:\.\d{2})?)$'
        ]

        # Skip lines that are clearly not items
        skip_patterns = [
            r'^\s*(?:total|subtotal|grand\s+total|tax|vat|shipping|discount|balance|amount\s+due|due|paid|payment)',
            r'^\s*(?:page|invoice|receipt|bill|statement)',
            r'^\s*(?:date|time|address|phone|email|website)',
            r'^\s*(?:thank\s+you|thanks|regards|sincerely)',
            r'^\s*[=\-_]{3,}',  # Separator lines
            r'^\s*\d+\s*$',  # Just numbers
            r'^\s*[\$₹€£¥]\s*\d+(?:,\d{3})*(?:\.\d{2})?\s*$'  # Just prices
        ]

        for line in lines:
            line_clean = line.strip()
            line_lower = line_clean.lower()

            # Skip if line is too short or matches skip patterns
            if len(line_clean) < 5:
                continue

            skip_line = False
            for skip_pattern in skip_patterns:
                if re.match(skip_pattern, line_lower):
                    skip_line = True
                    break

            if skip_line:
                continue

            # Try to extract item and price
            for pattern in price_patterns:
                match = re.search(pattern, line_clean, re.IGNORECASE)
                if match:
                    description = match.group(1).strip()
                    price = match.group(2).strip()

                    # Clean description
                    description = re.sub(r'^\d+\.?\s*', '', description)  # Remove leading numbers
                    description = re.sub(r'\s+', ' ', description)  # Normalize spaces
                    description = description.strip(' .-()[]{}')

                    # Validate description and price
                    if (len(description) > 2 and
                        len(price) > 0 and
                        not re.match(r'^\d+$', description) and  # Not just numbers
                        not description.lower() in ['total', 'subtotal', 'grand total', 'tax', 'vat', 'shipping', 'discount', 'balance', 'due', 'paid'] and
                        not description.lower().startswith(('page', 'invoice', 'receipt', 'bill', 'statement', 'date', 'time')) and
                        float(price.replace(',', '')) > 0):  # Valid price

                        data["items"].append({
                            "description": description,
                            "price": price
                        })
                    break

    # Final cleanup and validation
    if data["buyer"]:
        data["buyer"] = data["buyer"].strip().title()
    if data["sender"]:
        data["sender"] = data["sender"].strip().title()
    if data["invoice_date"]:
        data["invoice_date"] = data["invoice_date"].strip().lower()

    return data

def normalize_text(text):
    """Normalize text for comparison by removing extra whitespace and converting to lowercase."""
    text = re.sub(r'\s+', ' ', text.strip())
    return text.lower()

def calculate_character_accuracy(reference_text, extracted_text):
    """Calculate character-level accuracy using edit distance."""
    ref_normalized = normalize_text(reference_text)
    ext_normalized = normalize_text(extracted_text)
    matcher = difflib.SequenceMatcher(None, ref_normalized, ext_normalized)
    similarity = matcher.ratio()
    return similarity * 100

def calculate_word_accuracy(reference_text, extracted_text):
    """Calculate word-level accuracy."""
    ref_words = normalize_text(reference_text).split()
    ext_words = normalize_text(extracted_text).split()
    matcher = difflib.SequenceMatcher(None, ref_words, ext_words)
    similarity = matcher.ratio()
    return similarity * 100

def load_ground_truth(pdf_path):
    """Load ground truth text file if it exists."""
    gt_path = pdf_path.with_suffix('.gt.txt')
    if gt_path.exists():
        return gt_path.read_text(encoding='utf-8')
    alt_gt_path = pdf_path.with_name(pdf_path.stem + '.truth.txt')
    if alt_gt_path.exists():
        return alt_gt_path.read_text(encoding='utf-8')

    return None

def estimate_ocr_quality(text):
    """Estimate OCR quality based on text characteristics."""
    if not text.strip():
        return 0.0

    total_chars = len(text)

    readable_chars = len(re.findall(r'[a-zA-Z0-9\s.,!?;:\-\'"()]', text))

    words = text.split()
    likely_words = 0
    for word in words:
        clean_word = re.sub(r'[^a-zA-Z]', '', word.lower())
        if len(clean_word) >= 2 and any(vowel in clean_word for vowel in 'aeiou'):
            likely_words += 1

    char_quality = (readable_chars / total_chars) * 100 if total_chars > 0 else 0
    word_quality = (likely_words / len(words)) * 100 if words else 0

    estimated_quality = (char_quality + word_quality) / 2

    return min(estimated_quality, 95.0)

def save_to_json(data, output_path):
    """Save structured data to JSON file."""
    json_path = output_path.with_suffix('.json')
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=4, ensure_ascii=False)
    print(f"JSON file saved to: {json_path}")
    return json_path

def save_to_csv(data, output_path):
    """Save structured data to CSV file with specific format."""
    csv_path = output_path.with_suffix('.csv')

    try:
        with open(csv_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            writer.writerow(['Buyer', 'Sender', 'Invoice Date', 'Item Description', 'Item Price'])

            if not data.get('items'):
                writer.writerow([
                    data.get('buyer', ''),
                    data.get('sender', ''),
                    data.get('invoice_date', ''),
                    '',
                    ''
                ])
            else:
                for item in data['items']:
                    writer.writerow([
                        data.get('buyer', ''),
                        data.get('sender', ''),
                        data.get('invoice_date', ''),
                        item.get('description', ''),
                        item.get('price', '')
                    ])

        print(f"CSV file saved to: {csv_path}")
        return csv_path
    except PermissionError:
        # Try alternative location if permission denied
        alt_csv_path = Path.cwd() / f"{output_path.stem}_output.csv"
        try:
            with open(alt_csv_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['Buyer', 'Sender', 'Invoice Date', 'Item Description', 'Item Price'])

                if not data.get('items'):
                    writer.writerow([
                        data.get('buyer', ''),
                        data.get('sender', ''),
                        data.get('invoice_date', ''),
                        '',
                        ''
                    ])
                else:
                    for item in data['items']:
                        writer.writerow([
                            data.get('buyer', ''),
                            data.get('sender', ''),
                            data.get('invoice_date', ''),
                            item.get('description', ''),
                            item.get('price', '')
                        ])

            print(f"CSV file saved to: {alt_csv_path}")
            return alt_csv_path
        except Exception as e:
            print(f"Error saving CSV file: {e}")
            return None

def load_ground_truth(pdf_path):
    """Load ground truth text file if it exists."""
    gt_path = pdf_path.with_suffix('.gt.txt')
    if gt_path.exists():
        return gt_path.read_text(encoding='utf-8')

    alt_gt_path = pdf_path.with_name(pdf_path.stem + '.truth.txt')
    if alt_gt_path.exists():
        return alt_gt_path.read_text(encoding='utf-8')

    return None

def main():
    import sys

    # Check if PDF path is provided as command line argument
    if len(sys.argv) > 1:
        input_doc_path = Path(sys.argv[1])
        if not input_doc_path.exists():
            print(f"Error: File '{input_doc_path}' not found.")
            return
    else:
        # Look for any PDF files in current directory and Data folder
        possible_locations = [
            Path("."),  # Current directory
            Path("Data"),  # Data folder
            Path("../Data"),  # Parent Data folder
        ]

        pdf_files = []
        for location in possible_locations:
            if location.exists():
                pdf_files.extend(location.glob("*.pdf"))

        if not pdf_files:
            print("No PDF files found. Please:")
            print("1. Place a PDF file in the current directory, or")
            print("2. Place a PDF file in the 'Data' folder, or")
            print("3. Run: python test.py <path_to_pdf_file>")
            return

        # Use the first PDF found
        input_doc_path = pdf_files[0].resolve()

    print(f"Processing PDF: {input_doc_path}")

    print("Starting OCR processing...")
    pipeline_options = PdfPipelineOptions(
        do_ocr=True,
        force_full_page_ocr=True,
        ocr_options=ocr_options
    )
    converter = DocumentConverter(
        format_options={
            InputFormat.PDF: PdfFormatOption(
                pipeline_options=pipeline_options,
            )
        }
    )
    result = converter.convert(input_doc_path)
    extracted_text = result.document.export_to_text()
    output_txt_path = input_doc_path.with_suffix(".txt")
    output_txt_path.write_text(extracted_text, encoding='utf-8')

    print(f"OCR text saved to: {output_txt_path}")

    
    print("\n" + "="*50)
    print("STRUCTURED DATA EXTRACTION")
    print("="*50)

    model, processor = load_smoldocling_model()
    structured_data = None

    if model is not None and IMAGE_PROCESSING_AVAILABLE:
        try:
            print("Converting PDF to images for SmolDocling processing...")
            images = convert_from_path(str(input_doc_path))
            if images:
                print("Using SmolDocling for high-accuracy structured extraction...")
                structured_data = extract_structured_data_with_smoldocling(
                    images[0], model, processor
                )

                if structured_data:
                    print("✓ SmolDocling extraction successful!")
                else:
                    print("⚠ SmolDocling extraction failed, using pattern matching...")
        except Exception as e:
            print(f"⚠ SmolDocling processing failed: {e}")
    elif model is not None:
        print("⚠ Image processing not available, cannot use SmolDocling")

    
    if structured_data is None:
        print("Using pattern matching for structured data extraction...")
        structured_data = extract_structured_data_from_text(extracted_text)

    
    print("\n" + "="*50)
    print("EXTRACTED STRUCTURED DATA")
    print("="*50)
    print(f"Buyer: {structured_data.get('buyer', 'Not found')}")
    print(f"Sender: {structured_data.get('sender', 'Not found')}")
    print(f"Invoice Date: {structured_data.get('invoice_date', 'Not found')}")
    print(f"Number of Items: {len(structured_data.get('items', []))}")

    if structured_data.get('items'):
        print("\nItems:")
        for i, item in enumerate(structured_data['items'], 1):
            print(f"  {i}. {item.get('description', 'N/A')} - ${item.get('price', 'N/A')}")

    
    print("\n" + "="*50)
    print("SAVING OUTPUT FILES")
    print("="*50)

    
    json_path = save_to_json(structured_data, input_doc_path)

    
    csv_path = save_to_csv(structured_data, input_doc_path)

    print(f"\n✓ Processing complete!")
    print(f"✓ Raw text: {output_txt_path}")
    print(f"✓ JSON output: {json_path}")
    print(f"✓ CSV output: {csv_path}")

    return structured_data, json_path, csv_path

if __name__ == "__main__":
    main()
