"""
Configuration file for OCR model testing
"""
import os
from pathlib import Path

# Project paths
PROJECT_ROOT = Path(__file__).parent
INPUT_DIR = PROJECT_ROOT / "test_images"
OUTPUT_DIR = PROJECT_ROOT / "results"
MODELS_DIR = PROJECT_ROOT / "models"

# Create directories if they don't exist
INPUT_DIR.mkdir(exist_ok=True)
OUTPUT_DIR.mkdir(exist_ok=True)
MODELS_DIR.mkdir(exist_ok=True)

# OCR Models to test
OCR_MODELS = {
    'tesseract': {
        'enabled': True,
        'config': '--oem 3 --psm 6'  # OCR Engine Mode 3, Page Segmentation Mode 6
    },
    'easyocr': {
        'enabled': True,
        'languages': ['en'],
        'gpu': True  # Set to False if no GPU available
    },
    'paddleocr': {
        'enabled': True,
        'lang': 'en',
        'use_gpu': True  # Set to False if no GPU available
    },
    'trocr': {
        'enabled': True,
        'model_name': 'microsoft/trocr-base-handwritten',
        'device': 'cuda'  # Change to 'cpu' if no GPU available
    }
}

# Output formats
OUTPUT_FORMATS = {
    'text': True,
    'json': True,
    'csv': True,
    'detailed_report': True
}

# Image preprocessing settings
IMAGE_PREPROCESSING = {
    'resize': True,
    'max_width': 2000,
    'max_height': 2000,
    'enhance_contrast': True,
    'denoise': True,
    'binarize': False  # Set to True for very poor quality images
}

# Evaluation metrics
METRICS = {
    'character_accuracy': True,
    'word_accuracy': True,
    'levenshtein_distance': True,
    'confidence_scores': True,
    'processing_time': True
}

# File extensions to process
SUPPORTED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']

# Logging configuration
LOGGING = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': OUTPUT_DIR / 'ocr_testing.log'
}
