#!/usr/bin/env python3
"""
Simple setup script for basic OCR testing
"""
import subprocess
import sys
import os
from pathlib import Path

def install_basic_requirements():
    """Install basic OCR requirements"""
    print("Installing basic OCR requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "simple_requirements.txt"])
        print("✓ Basic requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Error installing requirements: {e}")
        return False

def check_tesseract():
    """Check if Tesseract is installed"""
    print("Checking Tesseract installation...")
    try:
        result = subprocess.run(["tesseract", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.split('\n')[0]
            print(f"✓ {version}")
            return True
        else:
            print("✗ Tesseract not found")
            return False
    except FileNotFoundError:
        print("✗ Tesseract not found")
        return False

def setup_env_file():
    """Setup .env file with configuration"""
    env_file = Path(".env")
    
    if env_file.exists():
        print("✓ .env file already exists")
        return
    
    print("Creating .env configuration file...")
    
    # Try to detect Tesseract path
    tesseract_path = ""
    
    # Common Windows paths
    windows_paths = [
        r"C:\Program Files\Tesseract-OCR\tesseract.exe",
        r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        r"C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe".format(os.getenv('USERNAME', ''))
    ]
    
    # Check Windows paths
    for path in windows_paths:
        if Path(path).exists():
            tesseract_path = path
            break
    
    # Check if tesseract is in PATH
    if not tesseract_path:
        try:
            result = subprocess.run(["where", "tesseract"], capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                tesseract_path = result.stdout.strip().split('\n')[0]
        except:
            pass
    
    # Create .env file
    env_content = f"""# OCR Configuration Environment Variables

# Tesseract OCR Path
# Set this if Tesseract is not in your system PATH
TESSERACT_PATH={tesseract_path}

# GPU Configuration (set to false for CPU-only)
USE_GPU=false

# Output Configuration
OUTPUT_DIR=results
INPUT_DIR=test_images

# Logging Level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO
"""
    
    with open(env_file, 'w') as f:
        f.write(env_content)
    
    print(f"✓ Created .env file")
    if tesseract_path:
        print(f"  Auto-detected Tesseract path: {tesseract_path}")
    else:
        print("  ⚠ Could not auto-detect Tesseract path")
        print("  Please edit .env file and set TESSERACT_PATH manually")

def create_directories():
    """Create necessary directories"""
    directories = ["test_images", "results", "results/text_files", "results/json_files"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✓ Created directories")

def print_usage_instructions():
    """Print usage instructions"""
    print("\n" + "="*60)
    print("SETUP COMPLETE!")
    print("="*60)
    
    print("\n📋 USAGE INSTRUCTIONS:")
    print("\n1. Basic usage (Tesseract + EasyOCR):")
    print("   python simple_ocr.py")
    
    print("\n2. Select specific models:")
    print("   python simple_ocr.py --models tesseract")
    print("   python simple_ocr.py --models easyocr")
    print("   python simple_ocr.py --models tesseract easyocr")
    
    print("\n3. Test single image:")
    print("   python simple_ocr.py --single-image path/to/image.jpg")
    
    print("\n4. Custom input directory:")
    print("   python simple_ocr.py --input-dir my_images")
    
    print("\n📁 DIRECTORIES:")
    print("   test_images/     - Put your test images here")
    print("   results/         - OCR results will be saved here")
    print("   .env             - Configuration file")
    
    print("\n🔧 ADVANCED MODELS:")
    print("To enable PaddleOCR:")
    print("   pip install paddleocr")
    print("   python simple_ocr.py --models paddleocr")
    
    print("\nTo enable TrOCR (best for handwriting):")
    print("   pip install transformers torch")
    print("   python simple_ocr.py --models trocr")
    
    print("\n⚠ TROUBLESHOOTING:")
    print("- If Tesseract fails, edit .env file and set TESSERACT_PATH")
    print("- For GPU acceleration, set USE_GPU=true in .env file")
    print("- Add test images to test_images/ directory")

def main():
    print("="*60)
    print("SIMPLE OCR SETUP")
    print("="*60)
    
    # Create directories
    create_directories()
    
    # Setup .env file
    setup_env_file()
    
    # Check Tesseract
    tesseract_ok = check_tesseract()
    
    # Install requirements
    if install_basic_requirements():
        print("\n✓ Basic setup completed successfully")
        
        if not tesseract_ok:
            print("\n⚠ WARNING: Tesseract OCR not found!")
            print("Please install Tesseract:")
            print("  Windows: https://github.com/UB-Mannheim/tesseract/wiki")
            print("  macOS: brew install tesseract")
            print("  Linux: sudo apt-get install tesseract-ocr")
            print("\nThen edit .env file to set TESSERACT_PATH if needed")
        
        print_usage_instructions()
    else:
        print("\n✗ Setup failed. Please check the error messages above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
