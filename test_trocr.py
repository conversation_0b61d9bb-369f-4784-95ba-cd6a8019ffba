#!/usr/bin/env python3
"""
Test script for TrOCR handwritten text recognition
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_trocr_availability():
    """Test if TrOCR models can be loaded."""
    print("🔍 Testing TrOCR Model Availability")
    print("=" * 50)
    
    try:
        from transformers import TrOCRProcessor, VisionEncoderDecoderModel
        print("✓ TrOCR imports successful")
        
        # Test model loading
        print("📥 Loading TrOCR processor...")
        processor = TrOCRProcessor.from_pretrained("microsoft/trocr-large-handwritten")
        print("✓ TrOCR processor loaded")
        
        print("📥 Loading TrOCR model...")
        model = VisionEncoderDecoderModel.from_pretrained("microsoft/trocr-large-handwritten")
        print("✓ TrOCR model loaded")
        
        print("🎉 TrOCR is ready for handwritten text recognition!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Install missing packages: pip install transformers sentencepiece")
        return False
    except Exception as e:
        print(f"❌ Model loading error: {e}")
        print("💡 This might be a network issue or model download problem")
        return False

def test_enhanced_parser():
    """Test the enhanced parser with TrOCR."""
    print("\n🧪 Testing Enhanced Parser with TrOCR")
    print("=" * 50)
    
    # Check for GROQ API key
    if not os.getenv("GROQ_API_KEY"):
        print("⚠️  GROQ_API_KEY not found - LLM correction won't work")
        print("Set GROQ_API_KEY in .env file for full functionality")
    
    try:
        from parser import DocumentParser
        print("✓ Enhanced parser imported successfully")
        
        # Initialize parser with handwritten mode
        print("🖋️  Initializing parser with TrOCR handwritten mode...")
        parser = DocumentParser(enable_handwritten_mode=True, use_vision_model=False)
        
        if parser.trocr_model and parser.trocr_processor:
            print("✓ TrOCR model initialized in parser")
        else:
            print("❌ TrOCR model failed to initialize in parser")
            return False
        
        print("🎉 Enhanced parser with TrOCR is ready!")
        return True
        
    except Exception as e:
        print(f"❌ Parser initialization failed: {e}")
        return False

def show_usage_examples():
    """Show usage examples for TrOCR handwritten processing."""
    print("\n📋 TrOCR Handwritten Processing Usage")
    print("=" * 50)
    
    print("🖋️  Command Examples:")
    print()
    print("# Process handwritten document with TrOCR")
    print("python 2test.py --input handwritten_receipt.jpg --output results.csv --handwritten")
    print()
    print("# Batch process handwritten documents")
    print("python 2test.py --input ./handwritten_docs/ --output batch_results.csv --batch --handwritten")
    print()
    print("# Handwritten processing with JSON output")
    print("python 2test.py --input receipt.png --output results.csv --handwritten --save-json")
    print()
    print("# Verbose handwritten processing")
    print("python 2test.py --input document.jpg --output results.csv --handwritten --verbose")
    
    print("\n🎯 TrOCR Features:")
    print("• Specialized transformer model for handwritten text")
    print("• Higher accuracy than traditional OCR for handwriting")
    print("• Automatic LLM correction for enhanced results")
    print("• Fallback to Tesseract OCR if TrOCR fails")
    print("• Complete invoice data extraction")
    print("• Enhanced CSV output with 'other_information' field")

def main():
    """Main test function."""
    print("🖋️  TrOCR Handwritten Text Recognition Test")
    print("=" * 60)
    
    # Test TrOCR availability
    trocr_available = test_trocr_availability()
    
    if trocr_available:
        # Test enhanced parser
        parser_ready = test_enhanced_parser()
        
        if parser_ready:
            show_usage_examples()
            
            print("\n✅ All tests passed! TrOCR handwritten processing is ready.")
            print("\n🚀 Next steps:")
            print("1. Add your handwritten invoice/receipt images")
            print("2. Run: python 2test.py --input your_file.jpg --output results.csv --handwritten")
            print("3. Check the enhanced CSV output with detailed extraction")
        else:
            print("\n❌ Parser initialization failed")
    else:
        print("\n❌ TrOCR not available")
        print("\n🔧 Installation steps:")
        print("1. pip install transformers>=4.35.0")
        print("2. pip install sentencepiece")
        print("3. Ensure stable internet for model download")

if __name__ == "__main__":
    main()
