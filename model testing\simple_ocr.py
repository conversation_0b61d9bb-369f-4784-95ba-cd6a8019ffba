#!/usr/bin/env python3
"""
Simple OCR Testing Script
Basic OCR functionality to extract text and save to .txt and .json files
"""

import os
import json
import time
import argparse
from pathlib import Path
from PIL import Image
import cv2
import numpy as np
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Create directories
def setup_directories():
    """Create necessary directories"""
    directories = ["test_images", "results", "results/text_files", "results/json_files"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    print("✓ Directories created")

class SimpleOCR:
    """Simple OCR class for basic text extraction"""

    def __init__(self, enabled_models=None):
        self.results = []
        self.enabled_models = enabled_models or ['tesseract', 'easyocr']
        print(f"Initializing Simple OCR with models: {', '.join(self.enabled_models)}")

        # Set Tesseract path from environment
        tesseract_path = os.getenv('TESSERACT_PATH')
        if tesseract_path and 'tesseract' in self.enabled_models:
            try:
                import pytesseract
                pytesseract.pytesseract.tesseract_cmd = tesseract_path
                print(f"✓ Tesseract path set to: {tesseract_path}")
            except ImportError:
                print("⚠ pytesseract not installed")
        
    def extract_text_tesseract(self, image_path):
        """Extract text using Tesseract OCR"""
        try:
            import pytesseract
            
            # Load and preprocess image
            image = cv2.imread(image_path)
            if image is None:
                return "", 0, "Could not load image"
            
            # Convert to grayscale for better OCR
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply some basic preprocessing
            # Resize if image is too small
            height, width = gray.shape
            if width < 300:
                scale = 300 / width
                new_width = int(width * scale)
                new_height = int(height * scale)
                gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            
            # Enhance contrast
            gray = cv2.convertScaleAbs(gray, alpha=1.2, beta=10)
            
            # Extract text
            start_time = time.time()
            extracted_text = pytesseract.image_to_string(gray, config='--oem 3 --psm 6')
            processing_time = time.time() - start_time
            
            # Get confidence score
            try:
                data = pytesseract.image_to_data(gray, output_type=pytesseract.Output.DICT)
                confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
                avg_confidence = sum(confidences) / len(confidences) if confidences else 0
            except:
                avg_confidence = 0
            
            return extracted_text.strip(), processing_time, avg_confidence, None
            
        except ImportError:
            return "", 0, 0, "pytesseract not installed. Install with: pip install pytesseract"
        except Exception as e:
            return "", 0, 0, str(e)
    
    def extract_text_easyocr(self, image_path):
        """Extract text using EasyOCR"""
        try:
            import easyocr

            # Initialize reader (this might take time on first run)
            if not hasattr(self, 'easyocr_reader'):
                print("Initializing EasyOCR (this may take a moment)...")
                use_gpu = os.getenv('USE_GPU', 'false').lower() == 'true'
                self.easyocr_reader = easyocr.Reader(['en'], gpu=use_gpu)
            
            start_time = time.time()
            results = self.easyocr_reader.readtext(image_path)
            processing_time = time.time() - start_time
            
            if not results:
                return "", processing_time, 0, None
            
            # Combine all detected text
            texts = []
            confidences = []
            
            for (bbox, text, confidence) in results:
                texts.append(text)
                confidences.append(confidence)
            
            combined_text = ' '.join(texts)
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0
            
            return combined_text, processing_time, avg_confidence * 100, None
            
        except ImportError:
            return "", 0, 0, "easyocr not installed. Install with: pip install easyocr"
        except Exception as e:
            return "", 0, 0, str(e)

    def extract_text_paddleocr(self, image_path):
        """Extract text using PaddleOCR"""
        try:
            from paddleocr import PaddleOCR

            # Initialize PaddleOCR (this might take time on first run)
            if not hasattr(self, 'paddle_ocr'):
                print("Initializing PaddleOCR (this may take a moment)...")
                use_gpu = os.getenv('USE_GPU', 'false').lower() == 'true'
                self.paddle_ocr = PaddleOCR(use_angle_cls=True, lang='en', use_gpu=use_gpu, show_log=False)

            start_time = time.time()
            results = self.paddle_ocr.ocr(image_path, cls=True)
            processing_time = time.time() - start_time

            if not results or not results[0]:
                return "", processing_time, 0, None

            texts = []
            confidences = []

            for line in results[0]:
                if line:
                    text = line[1][0]
                    confidence = line[1][1]
                    texts.append(text)
                    confidences.append(confidence)

            combined_text = ' '.join(texts)
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0

            return combined_text, processing_time, avg_confidence * 100, None

        except ImportError:
            return "", 0, 0, "paddleocr not installed. Install with: pip install paddleocr"
        except Exception as e:
            return "", 0, 0, str(e)

    def extract_text_trocr(self, image_path):
        """Extract text using TrOCR (Transformer OCR for handwritten text)"""
        try:
            from transformers import TrOCRProcessor, VisionEncoderDecoderModel
            from PIL import Image
            import torch

            # Initialize TrOCR (this might take time on first run)
            if not hasattr(self, 'trocr_processor'):
                print("Initializing TrOCR (this may take a moment)...")
                model_name = 'microsoft/trocr-base-handwritten'
                device = 'cuda' if torch.cuda.is_available() and os.getenv('USE_GPU', 'false').lower() == 'true' else 'cpu'

                self.trocr_processor = TrOCRProcessor.from_pretrained(model_name)
                self.trocr_model = VisionEncoderDecoderModel.from_pretrained(model_name)
                self.trocr_model.to(device)
                self.trocr_device = device

            # Load and process image
            image = Image.open(image_path).convert('RGB')

            start_time = time.time()
            pixel_values = self.trocr_processor(images=image, return_tensors="pt").pixel_values
            pixel_values = pixel_values.to(self.trocr_device)

            # Generate text
            generated_ids = self.trocr_model.generate(pixel_values)
            generated_text = self.trocr_processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
            processing_time = time.time() - start_time

            # TrOCR doesn't provide confidence scores directly
            confidence = min(90, len(generated_text) * 2) if generated_text.strip() else 0

            return generated_text.strip(), processing_time, confidence, None

        except ImportError:
            return "", 0, 0, "transformers or torch not installed. Install with: pip install transformers torch"
        except Exception as e:
            return "", 0, 0, str(e)
    
    def process_image(self, image_path, save_results=True):
        """Process a single image with available OCR methods"""
        print(f"\nProcessing: {image_path}")
        
        if not os.path.exists(image_path):
            print(f"Error: Image file not found: {image_path}")
            return None
        
        image_name = Path(image_path).stem
        results = {
            'image_path': image_path,
            'image_name': image_name,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'ocr_results': {}
        }
        
        # Test enabled OCR models
        ocr_methods = {
            'tesseract': self.extract_text_tesseract,
            'easyocr': self.extract_text_easyocr,
            'paddleocr': self.extract_text_paddleocr,
            'trocr': self.extract_text_trocr
        }

        for model_name in self.enabled_models:
            if model_name in ocr_methods:
                print(f"  Testing {model_name.upper()} OCR...")
                text, proc_time, confidence, error = ocr_methods[model_name](image_path)
                results['ocr_results'][model_name] = {
                    'extracted_text': text,
                    'processing_time': proc_time,
                    'confidence_score': confidence,
                    'success': error is None,
                    'error': error,
                    'text_length': len(text),
                    'word_count': len(text.split()) if text else 0
                }

                if error:
                    print(f"    ✗ {model_name.upper()} failed: {error}")
                else:
                    print(f"    ✓ {model_name.upper()}: {len(text)} chars, {confidence:.1f}% confidence, {proc_time:.2f}s")
        
        # Save results if requested
        if save_results:
            self.save_individual_results(results)
        
        self.results.append(results)
        return results
    
    def save_individual_results(self, result):
        """Save individual image results to text and JSON files"""
        image_name = result['image_name']
        
        # Save text files for each OCR method
        for ocr_method, ocr_data in result['ocr_results'].items():
            if ocr_data['success'] and ocr_data['extracted_text']:
                # Save as .txt file
                txt_file = Path("results/text_files") / f"{image_name}_{ocr_method}.txt"
                with open(txt_file, 'w', encoding='utf-8') as f:
                    f.write(ocr_data['extracted_text'])
                
                print(f"    💾 Saved text: {txt_file}")
        
        # Save complete result as JSON
        json_file = Path("results/json_files") / f"{image_name}_results.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"    💾 Saved JSON: {json_file}")
    
    def process_directory(self, directory_path="test_images"):
        """Process all images in a directory"""
        directory = Path(directory_path)
        
        if not directory.exists():
            print(f"Error: Directory not found: {directory_path}")
            return
        
        # Find image files
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
        image_files = []
        
        for ext in image_extensions:
            image_files.extend(directory.glob(f"*{ext}"))
            image_files.extend(directory.glob(f"*{ext.upper()}"))
        
        if not image_files:
            print(f"No image files found in {directory_path}")
            print("Supported formats: JPG, JPEG, PNG, BMP, TIFF, TIF")
            return
        
        print(f"Found {len(image_files)} images to process")
        
        for image_file in image_files:
            self.process_image(str(image_file))
        
        # Save summary
        self.save_summary()
    
    def save_summary(self):
        """Save a summary of all results"""
        if not self.results:
            print("No results to save")
            return
        
        summary = {
            'total_images': len(self.results),
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'summary_by_method': {},
            'detailed_results': self.results
        }
        
        # Calculate summary statistics
        methods = self.enabled_models
        
        for method in methods:
            successful = 0
            total_time = 0
            total_confidence = 0
            total_text_length = 0
            
            for result in self.results:
                if method in result['ocr_results']:
                    ocr_result = result['ocr_results'][method]
                    if ocr_result['success']:
                        successful += 1
                        total_time += ocr_result['processing_time']
                        total_confidence += ocr_result['confidence_score']
                        total_text_length += ocr_result['text_length']
            
            summary['summary_by_method'][method] = {
                'total_tests': len(self.results),
                'successful_tests': successful,
                'success_rate': successful / len(self.results) if self.results else 0,
                'avg_processing_time': total_time / successful if successful > 0 else 0,
                'avg_confidence': total_confidence / successful if successful > 0 else 0,
                'avg_text_length': total_text_length / successful if successful > 0 else 0
            }
        
        # Save summary
        summary_file = Path("results") / "ocr_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Summary saved: {summary_file}")
        
        # Print summary to console
        self.print_summary(summary)
    
    def print_summary(self, summary):
        """Print summary to console"""
        print("\n" + "="*60)
        print("SIMPLE OCR TESTING SUMMARY")
        print("="*60)
        
        for method, stats in summary['summary_by_method'].items():
            print(f"\n{method.upper()}:")
            print(f"  Success Rate: {stats['successful_tests']}/{stats['total_tests']} ({stats['success_rate']:.1%})")
            if stats['successful_tests'] > 0:
                print(f"  Avg Processing Time: {stats['avg_processing_time']:.2f}s")
                print(f"  Avg Confidence: {stats['avg_confidence']:.1f}%")
                print(f"  Avg Text Length: {stats['avg_text_length']:.0f} characters")
        
        print(f"\nResults saved in 'results/' directory")
        print("- Text files: results/text_files/")
        print("- JSON files: results/json_files/")
        print("- Summary: results/ocr_summary.json")

def create_sample_image():
    """Create a simple sample image for testing"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # Create a simple text image
        img = Image.new('RGB', (600, 150), color='white')
        draw = ImageDraw.Draw(img)
        
        # Try to use a default font
        try:
            font = ImageFont.truetype("arial.ttf", 30)
        except:
            font = ImageFont.load_default()
        
        text = "Hello World!\nThis is a simple OCR test."
        draw.text((20, 40), text, fill='black', font=font)
        
        sample_file = Path("test_images") / "sample_text.png"
        img.save(sample_file)
        print(f"✓ Created sample image: {sample_file}")
        
    except ImportError:
        print("⚠ PIL not available. Please add your own test images to test_images/ directory")

def main():
    """Main function with command-line interface"""
    import argparse

    parser = argparse.ArgumentParser(description='Simple OCR Testing with Model Selection')
    parser.add_argument('--models', nargs='+',
                       choices=['tesseract', 'easyocr', 'paddleocr', 'trocr'],
                       default=['tesseract', 'easyocr'],
                       help='OCR models to test (default: tesseract easyocr)')
    parser.add_argument('--input-dir', type=str, default='test_images',
                       help='Directory containing test images (default: test_images)')
    parser.add_argument('--single-image', type=str,
                       help='Test a single image file')
    parser.add_argument('--output-dir', type=str, default='results',
                       help='Output directory (default: results)')

    args = parser.parse_args()

    print("="*60)
    print("SIMPLE OCR TESTING")
    print("="*60)
    print(f"Selected models: {', '.join(args.models)}")

    # Setup
    setup_directories()

    # Check .env file
    if not Path('.env').exists():
        print("\n⚠ .env file not found. Creating template...")
        print("Please edit .env file to set TESSERACT_PATH if needed")

    # Initialize OCR with selected models
    ocr = SimpleOCR(enabled_models=args.models)

    if args.single_image:
        # Test single image
        if not Path(args.single_image).exists():
            print(f"Error: Image file not found: {args.single_image}")
            return

        print(f"\nTesting single image: {args.single_image}")
        result = ocr.process_image(args.single_image)
        if result:
            print("\nResults:")
            for model, data in result['ocr_results'].items():
                if data['success']:
                    print(f"  {model.upper()}: '{data['extracted_text'][:50]}...'")
                else:
                    print(f"  {model.upper()}: Failed - {data['error']}")
    else:
        # Test directory
        if not Path(args.input_dir).exists():
            print(f"Creating input directory: {args.input_dir}")
            Path(args.input_dir).mkdir(exist_ok=True)
            create_sample_image()

        # Check if test images exist
        test_dir = Path(args.input_dir)
        image_extensions = ['*.png', '*.jpg', '*.jpeg', '*.bmp', '*.tiff', '*.tif']
        image_files = []
        for ext in image_extensions:
            image_files.extend(test_dir.glob(ext))
            image_files.extend(test_dir.glob(ext.upper()))

        if not image_files:
            print(f"\nNo test images found in {args.input_dir}!")
            print("Please add image files to the directory")
            print("Supported formats: PNG, JPG, JPEG, BMP, TIFF")
            return

        print(f"\nFound {len(image_files)} test images")

        # Process all images
        ocr.process_directory(args.input_dir)

    print("\n" + "="*60)
    print("TESTING COMPLETE!")
    print("="*60)
    print(f"Results saved in '{args.output_dir}' directory")
    print("- Text files: results/text_files/")
    print("- JSON files: results/json_files/")
    print("- Summary: results/ocr_summary.json")

if __name__ == "__main__":
    main()
