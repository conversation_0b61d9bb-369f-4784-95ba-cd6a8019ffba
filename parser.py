import os
import time
import logging
import csv
from pathlib import Path
from typing import Optional, Union, List, Dict, Any
import json
import re



import pytesseract
from PIL import Image
import fitz


from groq import Groq
import instructor


# No additional imports needed - using Groq Llama-4-Scout for handwritten text


from models import InvoiceData, DocumentParsingResult, LineItem, ContactInfo, TaxInfo


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DocumentParser:

    def __init__(self, groq_api_key: Optional[str] = None, enable_handwritten_mode: bool = False):
        self.groq_api_key = groq_api_key or os.getenv("GROQ_API_KEY")
        if not self.groq_api_key:
            raise ValueError("GROQ_API_KEY environment variable or parameter required")


        self.groq_client = Groq(api_key=self.groq_api_key)
        self.client = instructor.from_groq(self.groq_client)


        self.enable_handwritten_mode = enable_handwritten_mode

        # Handwritten mode uses Llam<PERSON>-4-Scout via Groq API
        if enable_handwritten_mode:
            logger.info("Handwritten mode enabled - using enhanced OCR + Llama model via Groq for text extraction")


        self.supported_extensions = {'.pdf', '.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.txt', '.webp', '.gif'}
    
    def detect_document_type(self, file_path: Path) -> str:
        extension = file_path.suffix.lower()

        if extension == '.pdf':
            return 'pdf'
        elif extension in {'.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.webp', '.gif'}:
            return 'image'
        elif extension == '.txt':
            return 'text'
        else:
            raise ValueError(f"Unsupported file type: {extension}")
    
    def extract_text_from_pdf(self, file_path: Path) -> str:
        try:
            doc = fitz.open(file_path)
            text = ""
            
            for page_num in range(doc.page_count):
                page = doc[page_num]
                text += page.get_text()
            
            doc.close()
            return text.strip()
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {e}")
            
            return self.extract_text_with_ocr(file_path)
    




    def _check_tesseract_available(self) -> bool:
        """Check if Tesseract OCR is available."""
        try:
            import pytesseract
            pytesseract.get_tesseract_version()
            return True
        except Exception:
            return False

    def _extract_text_with_llama_enhanced_ocr(self, image: Image.Image) -> str:
        """Extract text using enhanced OCR + Llama model for handwritten text processing."""
        try:
            logger.info("Processing handwritten image with enhanced OCR + Llama model...")

            # First, extract text using multiple OCR approaches
            ocr_text = self._extract_handwritten_from_image(image)

            if not ocr_text.strip():
                logger.warning("No text extracted from OCR, cannot process with Llama")
                return ""

            logger.info(f"OCR extracted {len(ocr_text)} characters, enhancing with Llama model...")

            # Create enhanced prompt for handwritten text correction and interpretation
            prompt = f"""You are an expert at interpreting and correcting OCR text from handwritten invoices and receipts.

The following text was extracted from a handwritten document using OCR and may contain errors or unclear characters.

HANDWRITTEN OCR CORRECTION INSTRUCTIONS:
1. CORRECT OCR ERRORS: Fix common OCR mistakes in handwritten text:
   - Numbers: 0/O, 1/l/I, 5/S, 6/G, 8/B, 2/Z, 9/g
   - Letters: rn/m, cl/d, vv/w, ii/n, u/v, c/e
2. INTERPRET UNCLEAR TEXT: Make intelligent guesses for partially legible words
3. BUSINESS CONTEXT: Apply invoice/receipt knowledge:
   - Total amounts are usually at the bottom
   - Dates are often at the top
   - Item names followed by prices
   - Common abbreviations (qty, ea, tot, etc.)
4. PRESERVE STRUCTURE: Maintain the original layout and organization
5. ENHANCE READABILITY: Clean up spacing and formatting while preserving meaning

OCR EXTRACTED TEXT:
{ocr_text}

Please return the corrected and enhanced text, maintaining the original structure but fixing OCR errors and improving readability. Focus on accuracy for numbers, dates, and amounts."""

            # Call Llama model for text enhancement
            response = self.groq_client.chat.completions.create(
                model="llama-3.3-70b-versatile",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert at correcting OCR errors in handwritten business documents. Focus on accuracy and preserve the original structure."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.1,
                max_tokens=2048
            )

            enhanced_text = response.choices[0].message.content.strip()
            logger.info(f"Llama model enhanced text to {len(enhanced_text)} characters")

            return enhanced_text

        except Exception as e:
            logger.error(f"Enhanced OCR + Llama processing failed: {e}")
            # Fallback to regular OCR
            return self._extract_handwritten_from_image(image)

    def extract_text_handwritten(self, file_path: Path) -> str:
        """Specialized extraction for handwritten documents using Llama-4-Scout via Groq."""

        if not self.enable_handwritten_mode:
            logger.warning("Handwritten mode not enabled, falling back to regular OCR")
            return self._fallback_handwritten_extraction(file_path)

        try:
            if file_path.suffix.lower() == '.pdf':
                doc = fitz.open(file_path)
                all_text = ""

                for page_num in range(doc.page_count):
                    page = doc[page_num]
                    # Higher resolution for handwritten text
                    pix = page.get_pixmap(matrix=fitz.Matrix(3, 3))
                    img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

                    # Process with enhanced OCR + Llama
                    page_text = self._extract_text_with_llama_enhanced_ocr(img)
                    all_text += page_text + "\n"

                doc.close()
                return all_text.strip()
            else:
                img = Image.open(file_path)
                return self._extract_text_with_llama_enhanced_ocr(img)

        except Exception as e:
            logger.error(f"Error during enhanced OCR + Llama handwritten extraction: {e}")
            # Fallback to Tesseract if enhanced processing fails
            return self._fallback_handwritten_extraction(file_path)

    def _fallback_handwritten_extraction(self, file_path: Path) -> str:
        """Fallback handwritten extraction using Tesseract or vision model."""

        # Try Tesseract first
        tesseract_available = self._check_tesseract_available()

        if tesseract_available:
            logger.info("Using Tesseract OCR as fallback for handwritten text")
            try:
                if file_path.suffix.lower() == '.pdf':
                    doc = fitz.open(file_path)
                    all_text = ""
                    for page_num in range(doc.page_count):
                        page = doc[page_num]
                        pix = page.get_pixmap(matrix=fitz.Matrix(3, 3))
                        img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
                        page_text = self._extract_handwritten_from_image(img)
                        all_text += page_text + "\n"
                    doc.close()
                    return all_text.strip()
                else:
                    img = Image.open(file_path)
                    return self._extract_handwritten_from_image(img)
            except Exception as e:
                logger.error(f"Tesseract fallback failed: {e}")

        logger.error("All handwritten extraction methods failed")
        return ""

    def _extract_handwritten_from_image(self, img: Image.Image) -> str:
        """Extract text from handwritten image with multiple OCR approaches and LLM correction."""

        # Check if Tesseract is available first
        if not self._check_tesseract_available():
            logger.error("Tesseract not available and no alternative OCR method configured")
            return ""

        logger.info("Starting handwritten text extraction with multiple OCR approaches...")

        # Try multiple preprocessing approaches
        preprocessing_variants = [
            ("original", img),
            ("enhanced", self._preprocess_for_handwriting(img)),
            ("high_contrast", self._preprocess_high_contrast(img)),
            ("minimal", self._preprocess_minimal(img))
        ]

        all_ocr_results = []

        for variant_name, processed_img in preprocessing_variants:
            logger.debug(f"Trying OCR with {variant_name} preprocessing...")

            # Multiple OCR configurations
            configs = [
                ("psm6_whitelist", r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,!?@#$%&*()_+-=[]{}|;:,.<>?/" '),
                ("psm6_no_whitelist", r'--oem 3 --psm 6'),
                ("psm7", r'--oem 3 --psm 7'),
                ("psm8", r'--oem 3 --psm 8'),
                ("psm11", r'--oem 3 --psm 11'),
                ("psm13", r'--oem 3 --psm 13'),
                ("legacy_psm6", r'--oem 0 --psm 6'),
                ("lstm_psm6", r'--oem 1 --psm 6')
            ]

            for config_name, config in configs:
                try:
                    text = pytesseract.image_to_string(processed_img, config=config)
                    if text and text.strip():
                        result_info = f"{variant_name}_{config_name}"
                        all_ocr_results.append((text.strip(), result_info))
                        logger.debug(f"OCR success with {result_info}: {len(text.strip())} chars")
                except Exception as e:
                    logger.debug(f"OCR failed with {variant_name}_{config_name}: {e}")
                    continue

        # If we have OCR results, process them
        if all_ocr_results:
            # Choose the longest result as it's likely most complete
            best_text, best_method = max(all_ocr_results, key=lambda x: len(x[0]))
            logger.info(f"Best OCR result from {best_method}: {len(best_text)} characters")

            # Send to LLM for correction
            corrected_text = self._correct_text_with_llm(best_text)
            return corrected_text

        logger.error("All OCR text extraction methods failed")
        return ""

    def _preprocess_for_handwriting(self, img: Image.Image) -> Image.Image:
        """Preprocess image for better handwritten text recognition."""
        import cv2
        import numpy as np

        try:
            # Convert PIL to OpenCV
            img_array = np.array(img)
            if len(img_array.shape) == 3:
                img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
            else:
                img_cv = img_array

            # Convert to grayscale
            if len(img_cv.shape) == 3:
                gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
            else:
                gray = img_cv

            # Apply adaptive thresholding for handwritten text
            thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)

            # Noise reduction
            denoised = cv2.medianBlur(thresh, 3)

            # Slight dilation to connect broken characters
            kernel = np.ones((2,2), np.uint8)
            processed = cv2.morphologyEx(denoised, cv2.MORPH_CLOSE, kernel)

            # Convert back to PIL
            return Image.fromarray(processed)

        except Exception as e:
            logger.warning(f"Image preprocessing failed: {e}, using original image")
            return img

    def _preprocess_high_contrast(self, img: Image.Image) -> Image.Image:
        """High contrast preprocessing for difficult handwritten text."""
        try:
            import cv2
            import numpy as np

            # Convert to numpy array
            img_array = np.array(img)
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array

            # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)

            # Binary thresholding
            _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            return Image.fromarray(binary)

        except Exception as e:
            logger.warning(f"High contrast preprocessing failed: {e}")
            return img

    def _preprocess_minimal(self, img: Image.Image) -> Image.Image:
        """Minimal preprocessing - just resize for better OCR."""
        try:
            # Resize image to improve OCR accuracy
            width, height = img.size
            # Scale up if image is small
            if width < 1000 or height < 1000:
                scale_factor = max(1000 / width, 1000 / height)
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)
                img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # Convert to grayscale if needed
            if img.mode != 'L':
                img = img.convert('L')

            return img

        except Exception as e:
            logger.warning(f"Minimal preprocessing failed: {e}")
            return img

    def _correct_text_with_llm(self, raw_text: str) -> str:
        """Use LLM to correct OCR errors in handwritten text."""

        # If text is very short or seems garbled, don't bother with LLM correction
        if len(raw_text.strip()) < 5:
            logger.info("Text too short for LLM correction, returning as-is")
            return raw_text

        try:
            correction_prompt = f"""
You are an expert at correcting OCR errors from handwritten receipts and invoices.

The following text was extracted from a handwritten document using OCR, but may contain errors due to poor handwriting recognition:

RAW OCR TEXT:
{raw_text}

Please correct any obvious OCR errors and return the corrected text. Focus on:
1. Fixing common OCR mistakes (0/O, 1/l/I, 5/S, 6/G, 8/B, 2/Z, etc.)
2. Correcting misspelled words that should be common invoice terms (total, subtotal, tax, qty, etc.)
3. Fixing number recognition errors in prices and quantities
4. Correcting business names and addresses
5. Maintaining the original structure and formatting
6. Only make corrections that are clearly errors - don't change content unnecessarily

IMPORTANT: If the text appears to be mostly gibberish or unreadable, just return "UNREADABLE_TEXT" so we know the OCR failed completely.

Return only the corrected text, maintaining the same structure as the input.
"""

            response = self.client.chat.completions.create(
                model="llama-3.3-70b-versatile",
                messages=[
                    {"role": "system", "content": "You are an expert OCR error correction specialist for handwritten documents. You help fix OCR mistakes while preserving the original meaning and structure."},
                    {"role": "user", "content": correction_prompt}
                ],
                temperature=0.1,
                max_tokens=2048
            )

            corrected_text = response.choices[0].message.content.strip()

            # Check if LLM determined text was unreadable
            if corrected_text == "UNREADABLE_TEXT":
                logger.warning("LLM determined OCR text was unreadable")
                return ""

            logger.info(f"Text corrected using LLM: {len(raw_text)} -> {len(corrected_text)} chars")
            return corrected_text

        except Exception as e:
            logger.error(f"LLM text correction failed: {e}")
            return raw_text  # Return original if correction fails

    def extract_text_with_ocr(self, file_path: Path) -> str:
        try:
            if file_path.suffix.lower() == '.pdf':

                doc = fitz.open(file_path)
                text = ""

                for page_num in range(doc.page_count):
                    page = doc[page_num]
                    pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # Higher resolution
                    img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)


                    custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,!?@#$%&*()_+-=[]{}|;:,.<>? '
                    page_text = pytesseract.image_to_string(img, config=custom_config)
                    text += page_text + "\n"

                doc.close()
                return text.strip()
            else:

                img = Image.open(file_path)
                custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,!?@#$%&*()_+-=[]{}|;:,.<>? '
                text = pytesseract.image_to_string(img, config=custom_config)
                return text.strip()

        except Exception as e:
            logger.error(f"Error during OCR: {e}")
            return ""
    
    def extract_text(self, file_path: Path) -> str:
        """Extract text from document based on type and mode."""
        doc_type = self.detect_document_type(file_path)

        if doc_type == 'text':
            return file_path.read_text(encoding='utf-8')
        elif doc_type == 'pdf':
            # Check if handwritten mode is enabled
            if self.enable_handwritten_mode:
                logger.info("Using TrOCR handwritten mode for PDF")
                return self.extract_text_handwritten(file_path)
            # Use regular PDF text extraction + OCR fallback
            return self.extract_text_from_pdf(file_path)
        elif doc_type == 'image':
            # Check if handwritten mode is enabled
            if self.enable_handwritten_mode:
                logger.info("Using TrOCR handwritten mode for image")
                return self.extract_text_handwritten(file_path)
            # Use regular OCR for images
            else:
                return self.extract_text_with_ocr(file_path)
        else:
            raise ValueError(f"Unsupported document type: {doc_type}")
    
    def create_extraction_prompt(self, text: str) -> str:
        return f"""
You are an expert invoice and document parser with exceptional accuracy. Extract ALL structured data from the following text.

CRITICAL EXTRACTION REQUIREMENTS:
1. INVOICE IDENTIFICATION:
   - Invoice number, purchase order number, reference numbers
   - Invoice date, due date, issue date
   - Document type (invoice, receipt, bill, etc.)

2. COMPANY INFORMATION (Extract EVERYTHING):
   - Vendor/Seller: Name, full address, phone, email, tax ID, website
   - Customer/Buyer: Name, full address, phone, email, account number
   - Billing and shipping addresses if different

3. LINE ITEMS (Extract ALL items with maximum detail):
   - Item name/description (clean and complete)
   - Item category/type if mentioned
   - Quantity (exact numbers, units)
   - Unit price and total price per item
   - SKU, product codes, part numbers

4. FINANCIAL DETAILS:
   - Subtotal before taxes and fees
   - Tax information: type (VAT, GST, Sales Tax), rate percentage, amount
   - Discount amounts and descriptions
   - Shipping/delivery charges
   - Additional fees or charges
   - Final total amount
   - Currency

5. PAYMENT & TERMS:
   - Payment terms (Net 30, Due on receipt, etc.)
   - Payment methods accepted
   - Bank details if provided
   - Late fees or penalties

6. ADDITIONAL INFORMATION:
   - Notes, comments, special instructions
   - Delivery dates, terms
   - Return policies
   - Warranty information
   - Any handwritten notes or annotations

7. OTHER INFORMATION:
   - Capture ANY other text, numbers, or details that don't fit the above categories
   - Include reference numbers, codes, signatures, stamps
   - Special markings, certifications, compliance info

PROCESSING GUIDELINES:
- Handle OCR errors and formatting issues intelligently
- Make reasonable assumptions for unclear text
- Ensure all monetary values are positive
- Clean up artifacts while preserving meaning
- If information appears in multiple places, use the most complete version
- For missing standard fields, check if the information exists elsewhere in the document

TEXT TO PARSE:
{text}

Extract according to the InvoiceData schema with maximum accuracy and completeness. Put any information that doesn't fit standard fields into 'other_information'.
"""
    
    def parse_handwritten_with_llm(self, text: str) -> Optional[InvoiceData]:
        """Specialized LLM parsing for handwritten documents with enhanced prompting."""
        try:
            prompt = self.create_handwritten_extraction_prompt(text)

            response = self.client.chat.completions.create(
                model="llama-3.3-70b-versatile",
                messages=[
                    {"role": "system", "content": "You are an expert at parsing handwritten receipts and invoices. You excel at interpreting unclear handwriting and making intelligent assumptions about partially legible text."},
                    {"role": "user", "content": prompt}
                ],
                response_model=InvoiceData,
                max_retries=3,
                temperature=0.1  # Lower temperature for more consistent results
            )

            return response

        except Exception as e:
            logger.error(f"Handwritten LLM parsing error: {e}")
            return None

    def create_handwritten_extraction_prompt(self, text: str) -> str:
        """Create a specialized prompt for handwritten document extraction."""
        return f"""
You are an expert at parsing handwritten receipts and invoices. The following text was extracted from a handwritten document and may contain OCR errors or unclear characters.

HANDWRITTEN DOCUMENT PARSING INSTRUCTIONS:
1. INTERPRET UNCLEAR TEXT: Make intelligent guesses for partially legible words
2. COMMON HANDWRITING ERRORS: Watch for these OCR mistakes:
   - Numbers: 0/O, 1/l/I, 5/S, 6/G, 8/B, 2/Z
   - Letters: rn/m, cl/d, vv/w, ii/n
3. CONTEXT CLUES: Use surrounding text to interpret unclear words
4. BUSINESS LOGIC: Apply common invoice/receipt patterns:
   - Total amounts are usually at the bottom
   - Dates are often at the top
   - Item names followed by prices
   - Tax calculations make mathematical sense

5. HANDWRITTEN PATTERNS:
   - Cursive writing may connect letters
   - Numbers might be written differently (European vs American style)
   - Abbreviations are common (qty, ea, tot, etc.)
   - Poor spacing between words

6. EXTRACT ALL INFORMATION:
   - Company names (even if partially legible)
   - Addresses (street, city, state, zip)
   - Phone numbers and emails
   - All line items with quantities and prices
   - Dates in any format
   - Payment information
   - Any handwritten notes or comments

7. MATHEMATICAL VALIDATION:
   - Verify that line items add up to subtotals
   - Check tax calculations
   - Ensure totals make sense

EXTRACTED TEXT FROM HANDWRITTEN DOCUMENT:
{text}

Parse this handwritten document according to the InvoiceData schema. Be thorough and make reasonable interpretations of unclear text. Put any unclear or additional handwritten notes in the 'other_information' field.
"""

    def parse_with_llm(self, text: str) -> Optional[InvoiceData]:
        try:
            prompt = self.create_extraction_prompt(text)

            response = self.client.chat.completions.create(
                model="llama-3.3-70b-versatile",
                messages=[
                    {"role": "system", "content": "You are an expert invoice parser. Extract structured data accurately from documents."},
                    {"role": "user", "content": prompt}
                ],
                response_model=InvoiceData,
                max_retries=3
            )

            return response

        except Exception as e:
            logger.error(f"LLM parsing error: {e}")
            return None
    
    def fallback_regex_extraction(self, text: str) -> InvoiceData:
        logger.info("Using fallback regex extraction")
        
        
        total_patterns = [
            r'total[:\s]*\$?([0-9,]+\.?[0-9]*)',
            r'amount[:\s]*\$?([0-9,]+\.?[0-9]*)',
            r'grand\s+total[:\s]*\$?([0-9,]+\.?[0-9]*)'
        ]
        
        date_patterns = [
            r'(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',
            r'(\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2})',
            r'((?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*\s+\d{1,2},?\s+\d{4})'
        ]
        
        
        total = 0.0
        for pattern in total_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                total_str = match.group(1).replace(',', '')
                try:
                    total = float(total_str)
                    break
                except ValueError:
                    continue
        
        
        invoice_date = None
        for pattern in date_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                invoice_date = match.group(1)
                break
        
        
        items = []
        lines = text.split('\n')
        for line in lines:
            
            price_match = re.search(r'(.+?)\s+\$?([0-9,]+\.?[0-9]*)', line)
            if price_match:
                name = price_match.group(1).strip()
                price_str = price_match.group(2).replace(',', '')
                try:
                    price = float(price_str)
                    if len(name) > 3 and price > 0:
                        items.append(LineItem(name=name, total_price=price))
                except ValueError:
                    continue
        
        return InvoiceData(
            total=total,
            invoice_date=invoice_date,
            items=items
        )
    
    def parse_handwritten_document(self, file_path: Union[str, Path]) -> DocumentParsingResult:
        """Specialized method for parsing handwritten documents with enhanced accuracy."""
        start_time = time.time()
        file_path = Path(file_path)

        if not file_path.exists():
            return DocumentParsingResult(
                success=False,
                error_message=f"File not found: {file_path}"
            )

        if file_path.suffix.lower() not in self.supported_extensions:
            return DocumentParsingResult(
                success=False,
                error_message=f"Unsupported file type: {file_path.suffix}"
            )

        try:
            logger.info(f"Processing handwritten document: {file_path}")

            # Enable handwritten mode temporarily
            original_handwritten_mode = self.enable_handwritten_mode
            self.enable_handwritten_mode = True

            # Extract text using handwritten-specific methods
            raw_text = self.extract_text(file_path)

            # Restore original mode
            self.enable_handwritten_mode = original_handwritten_mode

            if not raw_text.strip():
                return DocumentParsingResult(
                    success=False,
                    error_message="No text could be extracted from the handwritten document",
                    raw_text=raw_text
                )

            logger.info("Parsing handwritten text with enhanced LLM")
            invoice_data = self.parse_handwritten_with_llm(raw_text)

            if invoice_data is None:
                logger.warning("Handwritten LLM parsing failed, using fallback method")
                invoice_data = self.fallback_regex_extraction(raw_text)

            processing_time = time.time() - start_time

            return DocumentParsingResult(
                success=True,
                invoice_data=invoice_data,
                raw_text=raw_text,
                processing_time=processing_time,
                extraction_method="handwritten_llm"
            )

        except Exception as e:
            logger.error(f"Error parsing handwritten document: {e}")
            return DocumentParsingResult(
                success=False,
                error_message=str(e),
                processing_time=time.time() - start_time
            )

    def parse_document(self, file_path: Union[str, Path]) -> DocumentParsingResult:
        start_time = time.time()
        file_path = Path(file_path)

        if not file_path.exists():
            return DocumentParsingResult(
                success=False,
                error_message=f"File not found: {file_path}"
            )

        if file_path.suffix.lower() not in self.supported_extensions:
            return DocumentParsingResult(
                success=False,
                error_message=f"Unsupported file type: {file_path.suffix}"
            )

        try:

            logger.info(f"Extracting text from {file_path}")
            raw_text = self.extract_text(file_path)

            if not raw_text.strip():
                return DocumentParsingResult(
                    success=False,
                    error_message="No text could be extracted from the document",
                    raw_text=raw_text
                )


            logger.info("Parsing with LLM")
            invoice_data = self.parse_with_llm(raw_text)


            if invoice_data is None:
                logger.warning("LLM parsing failed, using fallback method")
                invoice_data = self.fallback_regex_extraction(raw_text)

            processing_time = time.time() - start_time

            return DocumentParsingResult(
                success=True,
                invoice_data=invoice_data,
                raw_text=raw_text,
                processing_time=processing_time,
                extraction_method="llm" if invoice_data else "regex"
            )

        except Exception as e:
            logger.error(f"Error parsing document: {e}")
            return DocumentParsingResult(
                success=False,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
    
    def batch_parse_handwritten(self, file_paths: List[Union[str, Path]]) -> List[DocumentParsingResult]:
        """Batch process handwritten documents with specialized handling."""
        results = []
        total_files = len(file_paths)

        logger.info(f"Starting batch processing of {total_files} handwritten documents")

        for i, file_path in enumerate(file_paths, 1):
            logger.info(f"Processing handwritten document {i}/{total_files}: {file_path}")
            result = self.parse_handwritten_document(file_path)
            results.append(result)

            if result.success:
                logger.info(f"✓ Successfully processed handwritten document {file_path}")
                if result.invoice_data:
                    logger.info(f"  Extracted total: {result.invoice_data.currency} {result.invoice_data.total}")
            else:
                logger.warning(f"✗ Failed to process handwritten document {file_path}: {result.error_message}")

        return results

    def batch_parse(self, file_paths: List[Union[str, Path]]) -> List[DocumentParsingResult]:
        results = []
        total_files = len(file_paths)

        for i, file_path in enumerate(file_paths, 1):
            logger.info(f"Processing {i}/{total_files}: {file_path}")
            result = self.parse_document(file_path)
            results.append(result)


            if result.success:
                logger.info(f"✓ Successfully processed {file_path}")
            else:
                logger.warning(f"✗ Failed to process {file_path}: {result.error_message}")

        return results

    def export_to_csv(self, results: List[DocumentParsingResult], output_path: Union[str, Path]) -> None:
        output_path = Path(output_path)
        csv_rows = []

        for result in results:
            if result.success and result.invoice_data:
                invoice_rows = result.invoice_data.to_csv_rows()
                csv_rows.extend(invoice_rows)
            else:
                # Create error row with all expected fields
                error_row = {
                    'invoice_number': '',
                    'invoice_date': '',
                    'due_date': '',
                    'purchase_order_number': '',
                    'vendor_name': '',
                    'vendor_address': '',
                    'vendor_phone': '',
                    'vendor_email': '',
                    'customer_name': '',
                    'customer_address': '',
                    'customer_phone': '',
                    'customer_email': '',
                    'currency': 'USD',
                    'subtotal': 0,
                    'tax_type': '',
                    'tax_rate': 0,
                    'tax_amount': 0,
                    'discount_amount': 0,
                    'shipping_amount': 0,
                    'total': 0,
                    'payment_terms': '',
                    'payment_method': '',
                    'notes': '',
                    'item_name': f"ERROR: {result.error_message}",
                    'item_category': '',
                    'quantity': 0,
                    'unit_price': 0,
                    'item_total': 0,
                    'other_information': ''
                }
                csv_rows.append(error_row)

        if csv_rows:
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=csv_rows[0].keys())
                writer.writeheader()
                writer.writerows(csv_rows)

            logger.info(f"Enhanced CSV results exported to: {output_path}")
        else:
            logger.warning("No data to export")

    def export_to_json(self, results: List[DocumentParsingResult], output_path: Union[str, Path]) -> None:
        """Export parsing results to JSON format."""
        output_path = Path(output_path)
        json_data = []

        for result in results:
            if result.success and result.invoice_data:
                # Convert invoice data to dictionary
                invoice_dict = result.invoice_data.model_dump()

                # Add metadata
                result_dict = {
                    'success': True,
                    'file_processed': True,
                    'extraction_method': result.extraction_method,
                    'invoice_data': invoice_dict
                }
            else:
                # Error case
                result_dict = {
                    'success': False,
                    'file_processed': False,
                    'error_message': result.error_message,
                    'extraction_method': result.extraction_method,
                    'invoice_data': None
                }

            json_data.append(result_dict)

        # Write JSON file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, default=str, ensure_ascii=False)

        logger.info(f"JSON results exported to: {output_path}")

    def export_handwritten_results(self, results: List[DocumentParsingResult], base_filename: str = "handwritten_results") -> None:
        """Export handwritten document results to both CSV and JSON formats."""

        # Export to CSV
        csv_path = f"{base_filename}.csv"
        self.export_to_csv(results, csv_path)

        # Export to JSON
        json_path = f"{base_filename}.json"
        self.export_to_json(results, json_path)

        # Print summary
        successful = sum(1 for r in results if r.success)
        total = len(results)

        logger.info(f"Handwritten document processing complete:")
        logger.info(f"  Successfully processed: {successful}/{total} documents")
        logger.info(f"  CSV output: {csv_path}")
        logger.info(f"  JSON output: {json_path}")

        return csv_path, json_path
